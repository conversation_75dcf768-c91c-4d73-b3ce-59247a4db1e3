"""
网页抓取器单元测试
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from mcp_web_fetcher.core.fetcher import WebFetcher, FetchResult
from mcp_web_fetcher.utils.exceptions import FetchError


@pytest.mark.unit
class TestWebFetcher:
    """WebFetcher单元测试类"""
    
    @pytest.mark.asyncio
    async def test_fetch_url_success(self, web_fetcher, sample_html):
        """测试成功抓取URL"""
        # 模拟HTTP响应
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.headers = {"content-type": "text/html"}
        mock_response.text = AsyncMock(return_value=sample_html)
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value = mock_response
            
            result = await web_fetcher.fetch_url("https://example.com")
            
            assert result.success is True
            assert result.status_code == 200
            assert result.content == sample_html
            assert result.url == "https://example.com"
    
    @pytest.mark.asyncio
    async def test_fetch_url_failure(self, web_fetcher):
        """测试抓取URL失败"""
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.side_effect = Exception("Network error")
            
            result = await web_fetcher.fetch_url("https://example.com")
            
            assert result.success is False
            assert result.error is not None
            assert "Network error" in result.error
    
    @pytest.mark.asyncio
    async def test_batch_fetch(self, web_fetcher, test_urls, sample_html):
        """测试批量抓取"""
        # 模拟HTTP响应
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.headers = {"content-type": "text/html"}
        mock_response.text = AsyncMock(return_value=sample_html)
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value = mock_response
            
            results = await web_fetcher.batch_fetch(test_urls[:2])
            
            assert len(results) == 2
            assert all(result.success for result in results)
            assert all(result.content == sample_html for result in results)
    
    @pytest.mark.asyncio
    async def test_fetch_with_proxy(self, web_fetcher, sample_html):
        """测试使用代理抓取"""
        # 配置代理管理器返回代理
        web_fetcher.proxy_manager.get_proxy.return_value = "http://proxy:8080"
        
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.headers = {"content-type": "text/html"}
        mock_response.text = AsyncMock(return_value=sample_html)
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value = mock_response
            
            result = await web_fetcher.fetch_url(
                "https://example.com",
                use_proxy=True
            )
            
            assert result.success is True
            # 验证代理被调用
            web_fetcher.proxy_manager.get_proxy.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_fetch_with_cache_hit(self, web_fetcher, sample_fetch_result):
        """测试缓存命中"""
        # 配置缓存管理器返回缓存结果
        web_fetcher.cache_manager.get.return_value = sample_fetch_result
        
        result = await web_fetcher.fetch_url("https://example.com")
        
        assert result == sample_fetch_result
        # 验证缓存被查询
        web_fetcher.cache_manager.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_fetch_with_cache_miss(self, web_fetcher, sample_html):
        """测试缓存未命中"""
        # 配置缓存管理器返回None (缓存未命中)
        web_fetcher.cache_manager.get.return_value = None
        
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.headers = {"content-type": "text/html"}
        mock_response.text = AsyncMock(return_value=sample_html)
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value = mock_response
            
            result = await web_fetcher.fetch_url("https://example.com")
            
            assert result.success is True
            # 验证缓存被查询和设置
            web_fetcher.cache_manager.get.assert_called_once()
            web_fetcher.cache_manager.set.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_retry_mechanism(self, web_fetcher, sample_html):
        """测试重试机制"""
        # 前两次失败，第三次成功
        mock_response_success = MagicMock()
        mock_response_success.status = 200
        mock_response_success.headers = {"content-type": "text/html"}
        mock_response_success.text = AsyncMock(return_value=sample_html)
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.side_effect = [
                Exception("First failure"),
                Exception("Second failure"),
                mock_get.return_value.__aenter__.return_value := mock_response_success
            ]
            
            result = await web_fetcher.fetch_url("https://example.com")
            
            assert result.success is True
            assert mock_get.call_count == 3
    
    def test_detect_fetch_method(self, web_fetcher):
        """测试抓取方法检测"""
        # 静态页面
        static_url = "https://example.com/page.html"
        method = web_fetcher._detect_fetch_method(static_url)
        assert method == "static"
        
        # 动态页面
        dynamic_url = "https://example.com/spa#page"
        method = web_fetcher._detect_fetch_method(dynamic_url)
        assert method == "dynamic"
    
    def test_extract_links(self, web_fetcher, sample_html):
        """测试链接提取"""
        html_with_links = """
        <html>
        <body>
            <a href="/page1">Page 1</a>
            <a href="https://example.com/page2">Page 2</a>
            <a href="../page3">Page 3</a>
        </body>
        </html>
        """
        
        links = web_fetcher.extract_links(html_with_links, "https://example.com/")
        
        assert len(links) == 3
        assert "https://example.com/page1" in links
        assert "https://example.com/page2" in links
        assert "https://example.com/page3" in links


@pytest.mark.unit
class TestFetchResult:
    """FetchResult单元测试类"""
    
    def test_fetch_result_creation(self):
        """测试FetchResult创建"""
        result = FetchResult(
            url="https://example.com",
            content="<html></html>",
            status_code=200,
            headers={"content-type": "text/html"},
            metadata={"method": "static"},
            timestamp=datetime.now(),
            success=True
        )
        
        assert result.url == "https://example.com"
        assert result.success is True
        assert result.error is None
    
    def test_fetch_result_with_error(self):
        """测试带错误的FetchResult"""
        result = FetchResult(
            url="https://example.com",
            content="",
            status_code=0,
            headers={},
            metadata={},
            timestamp=datetime.now(),
            success=False,
            error="Network timeout"
        )
        
        assert result.success is False
        assert result.error == "Network timeout"
