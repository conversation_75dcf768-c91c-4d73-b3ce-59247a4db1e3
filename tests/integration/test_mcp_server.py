"""
MCP服务器集成测试
"""

import pytest
import json
from unittest.mock import AsyncMock, patch

from mcp.types import CallToolRequest, ListToolsRequest
from mcp_web_fetcher.server import MCPServer


@pytest.mark.integration
class TestMCPServerIntegration:
    """MCP服务器集成测试类"""
    
    @pytest.mark.asyncio
    async def test_list_tools(self, mcp_server):
        """测试工具列表"""
        # 创建列表工具请求
        request = ListToolsRequest()
        
        # 调用处理器
        result = await mcp_server.server._tool_list_handler()
        
        assert len(result.tools) == 4
        tool_names = [tool.name for tool in result.tools]
        assert "fetch_url" in tool_names
        assert "batch_fetch" in tool_names
        assert "get_stats" in tool_names
        assert "clear_cache" in tool_names
    
    @pytest.mark.asyncio
    async def test_fetch_url_tool(self, mcp_server, sample_html):
        """测试fetch_url工具"""
        # 模拟抓取结果
        mock_fetch_result = AsyncMock()
        mock_fetch_result.success = True
        mock_fetch_result.content = sample_html
        mock_fetch_result.status_code = 200
        
        mcp_server.web_fetcher.fetch_url = AsyncMock(return_value=mock_fetch_result)
        
        # 模拟ML处理结果
        mock_ml_result = AsyncMock()
        mock_ml_result.success = True
        mock_ml_result.structured_data = {
            "title": "测试页面",
            "content": "这是一段测试内容。"
        }
        
        mcp_server.ml_engine.process = AsyncMock(return_value=mock_ml_result)
        
        # 创建工具调用请求
        request = CallToolRequest(
            name="fetch_url",
            arguments={
                "url": "https://example.com",
                "format": "json",
                "use_proxy": False,
                "use_cache": True,
                "use_ml": True
            }
        )
        
        # 调用工具
        result = await mcp_server._handle_fetch_url(request.arguments)
        
        assert result.isError is None or result.isError is False
        assert len(result.content) > 0
        
        # 验证返回的内容是有效的JSON
        content_text = result.content[0].text
        parsed_data = json.loads(content_text)
        assert "title" in parsed_data
        assert "content" in parsed_data
    
    @pytest.mark.asyncio
    async def test_batch_fetch_tool(self, mcp_server, test_urls, sample_html):
        """测试batch_fetch工具"""
        # 模拟批量抓取结果
        mock_fetch_results = []
        for url in test_urls[:2]:
            mock_result = AsyncMock()
            mock_result.success = True
            mock_result.content = sample_html
            mock_result.url = url
            mock_fetch_results.append(mock_result)
        
        mcp_server.web_fetcher.batch_fetch = AsyncMock(return_value=mock_fetch_results)
        
        # 模拟ML批量处理结果
        mock_ml_results = []
        for i, url in enumerate(test_urls[:2]):
            mock_result = AsyncMock()
            mock_result.success = True
            mock_result.structured_data = {
                "url": url,
                "title": f"页面 {i+1}",
                "content": f"内容 {i+1}"
            }
            mock_ml_results.append(mock_result)
        
        mcp_server.ml_engine.batch_process = AsyncMock(return_value=mock_ml_results)
        
        # 创建工具调用请求
        request = CallToolRequest(
            name="batch_fetch",
            arguments={
                "urls": test_urls[:2],
                "format": "json",
                "max_concurrent": 5
            }
        )
        
        # 调用工具
        result = await mcp_server._handle_batch_fetch(request.arguments)
        
        assert result.isError is None or result.isError is False
        assert len(result.content) > 0
        
        # 验证返回的内容是有效的JSON数组
        content_text = result.content[0].text
        parsed_data = json.loads(content_text)
        assert isinstance(parsed_data, list)
        assert len(parsed_data) == 2
    
    @pytest.mark.asyncio
    async def test_get_stats_tool(self, mcp_server):
        """测试get_stats工具"""
        # 创建工具调用请求
        request = CallToolRequest(
            name="get_stats",
            arguments={}
        )
        
        # 调用工具
        result = await mcp_server._handle_get_stats(request.arguments)
        
        assert result.isError is None or result.isError is False
        assert len(result.content) > 0
        
        # 验证返回的统计信息
        content_text = result.content[0].text
        stats = json.loads(content_text)
        
        assert "server" in stats
        assert "fetcher" in stats
        assert "proxy" in stats
        assert "ml_engine" in stats
    
    @pytest.mark.asyncio
    async def test_clear_cache_tool(self, mcp_server):
        """测试clear_cache工具"""
        # 创建工具调用请求
        request = CallToolRequest(
            name="clear_cache",
            arguments={"pattern": "test"}
        )
        
        # 调用工具
        result = await mcp_server._handle_clear_cache(request.arguments)
        
        assert result.isError is None or result.isError is False
        assert len(result.content) > 0
        
        # 验证缓存清理被调用
        mcp_server.cache_manager.clear.assert_called_once_with("test")
    
    @pytest.mark.asyncio
    async def test_error_handling(self, mcp_server):
        """测试错误处理"""
        # 模拟抓取失败
        mcp_server.web_fetcher.fetch_url = AsyncMock(side_effect=Exception("Network error"))
        
        # 创建工具调用请求
        request = CallToolRequest(
            name="fetch_url",
            arguments={"url": "https://invalid-url.com"}
        )
        
        # 调用工具
        result = await mcp_server._handle_fetch_url(request.arguments)
        
        assert result.isError is True
        assert "Error:" in result.content[0].text
    
    @pytest.mark.asyncio
    async def test_format_output_json(self, mcp_server):
        """测试JSON格式输出"""
        data = {"title": "测试", "content": "内容"}
        
        result = mcp_server._format_output(data, "json")
        
        # 验证是有效的JSON
        parsed = json.loads(result)
        assert parsed == data
    
    @pytest.mark.asyncio
    async def test_format_output_markdown(self, mcp_server):
        """测试Markdown格式输出"""
        data = {"title": "测试", "content": "内容"}
        
        result = mcp_server._format_output(data, "markdown")
        
        assert "# 结构化数据" in result
        assert "## title" in result
        assert "## content" in result
    
    @pytest.mark.asyncio
    async def test_format_output_text(self, mcp_server):
        """测试文本格式输出"""
        data = {"title": "测试", "content": "内容"}
        
        result = mcp_server._format_output(data, "text")
        
        assert str(data) == result


@pytest.mark.integration
@pytest.mark.slow
class TestMCPServerEndToEnd:
    """MCP服务器端到端测试"""
    
    @pytest.mark.asyncio
    async def test_full_pipeline_without_ml(self, mcp_server):
        """测试完整流程（不使用ML）"""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟HTTP响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.headers = {"content-type": "text/html"}
            mock_response.text = AsyncMock(return_value="<html><body>Test</body></html>")
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # 创建工具调用请求
            request = CallToolRequest(
                name="fetch_url",
                arguments={
                    "url": "https://httpbin.org/html",
                    "use_ml": False
                }
            )
            
            # 调用工具
            result = await mcp_server._handle_fetch_url(request.arguments)
            
            assert result.isError is None or result.isError is False
            content_text = result.content[0].text
            data = json.loads(content_text)
            assert "raw_content" in data
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, mcp_server, test_urls):
        """测试并发请求处理"""
        import asyncio
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟HTTP响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.headers = {"content-type": "text/html"}
            mock_response.text = AsyncMock(return_value="<html><body>Test</body></html>")
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # 创建多个并发请求
            tasks = []
            for url in test_urls[:3]:
                request = CallToolRequest(
                    name="fetch_url",
                    arguments={"url": url, "use_ml": False}
                )
                task = mcp_server._handle_fetch_url(request.arguments)
                tasks.append(task)
            
            # 并发执行
            results = await asyncio.gather(*tasks)
            
            # 验证所有请求都成功
            assert len(results) == 3
            for result in results:
                assert result.isError is None or result.isError is False
