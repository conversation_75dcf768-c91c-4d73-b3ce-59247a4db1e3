"""
pytest配置文件

定义测试夹具和配置。
"""

import asyncio
import pytest
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

from mcp_web_fetcher.config import Settings
from mcp_web_fetcher.core.fetcher import <PERSON>Fetcher, Fetch<PERSON><PERSON>ult
from mcp_web_fetcher.core.proxy import ProxyManager
from mcp_web_fetcher.core.cache import CacheManager
from mcp_web_fetcher.ml.engine import MLEng<PERSON>
from mcp_web_fetcher.server import MCPServer


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """临时目录夹具"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def test_settings(temp_dir):
    """测试配置夹具"""
    return Settings(
        environment="test",
        database=Settings.DatabaseConfig(
            host="localhost",
            port=5432,
            name="test_db",
            user="test_user",
            password="test_pass"
        ),
        redis=Settings.RedisConfig(
            host="localhost",
            port=6379,
            db=1
        ),
        ml=Settings.MLConfig(
            model_dir=temp_dir / "models",
            device="cpu",
            batch_size=2
        ),
        proxy=Settings.ProxyConfig(
            enabled=False
        ),
        fetcher=Settings.FetcherConfig(
            max_concurrent=2,
            timeout=10,
            cache_enabled=False
        ),
        mcp=Settings.MCPConfig(
            host="localhost",
            port=8001,
            debug=True
        ),
        logging=Settings.LoggingConfig(
            level="DEBUG",
            file_enabled=False
        )
    )


@pytest.fixture
def mock_proxy_manager():
    """模拟代理管理器"""
    manager = MagicMock(spec=ProxyManager)
    manager.enabled = False
    manager.get_proxy = AsyncMock(return_value=None)
    manager.get_stats = MagicMock(return_value={
        "enabled": False,
        "total": 0,
        "active": 0
    })
    manager.cleanup = AsyncMock()
    return manager


@pytest.fixture
def mock_cache_manager():
    """模拟缓存管理器"""
    manager = MagicMock(spec=CacheManager)
    manager.enabled = False
    manager.get = AsyncMock(return_value=None)
    manager.set = AsyncMock()
    manager.clear = AsyncMock()
    manager.get_stats = AsyncMock(return_value={
        "enabled": False,
        "memory_cache": {"size": 0, "max_size": 1000},
        "redis_cache": {"connected": False}
    })
    manager.cleanup = AsyncMock()
    return manager


@pytest.fixture
def sample_html():
    """示例HTML内容"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试页面</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>主标题</h1>
        <p>这是一段测试内容。</p>
        <div class="content">
            <h2>子标题</h2>
            <p>更多内容...</p>
        </div>
        <footer>页脚信息</footer>
    </body>
    </html>
    """


@pytest.fixture
def sample_fetch_result(sample_html):
    """示例抓取结果"""
    from datetime import datetime
    
    return FetchResult(
        url="https://example.com",
        content=sample_html,
        status_code=200,
        headers={"content-type": "text/html; charset=utf-8"},
        metadata={"method": "static", "content_length": len(sample_html)},
        timestamp=datetime.now(),
        success=True
    )


@pytest.fixture
async def web_fetcher(mock_proxy_manager, mock_cache_manager):
    """Web抓取器夹具"""
    fetcher = WebFetcher(
        proxy_manager=mock_proxy_manager,
        cache_manager=mock_cache_manager
    )
    
    async with fetcher:
        yield fetcher


@pytest.fixture
def mock_ml_engine():
    """模拟ML引擎"""
    engine = MagicMock(spec=MLEngine)
    engine.initialize = AsyncMock()
    engine.process = AsyncMock()
    engine.batch_process = AsyncMock()
    engine.get_stats = MagicMock(return_value={
        "total_processed": 0,
        "success_rate": 0.0,
        "average_processing_time": 0.0,
        "device": "cpu",
        "models_loaded": {
            "content_extraction": False,
            "layout_analysis": False,
            "classification": False,
            "cleaning": False
        }
    })
    engine.cleanup = AsyncMock()
    return engine


@pytest.fixture
async def mcp_server(mock_proxy_manager, mock_cache_manager, mock_ml_engine):
    """MCP服务器夹具"""
    server = MCPServer()
    
    # 替换为模拟组件
    server.proxy_manager = mock_proxy_manager
    server.cache_manager = mock_cache_manager
    server.ml_engine = mock_ml_engine
    server.web_fetcher.proxy_manager = mock_proxy_manager
    server.web_fetcher.cache_manager = mock_cache_manager
    
    yield server
    
    await server.cleanup()


# 测试数据
@pytest.fixture
def test_urls():
    """测试URL列表"""
    return [
        "https://httpbin.org/html",
        "https://httpbin.org/json",
        "https://example.com",
    ]


@pytest.fixture
def invalid_urls():
    """无效URL列表"""
    return [
        "not-a-url",
        "http://",
        "https://nonexistent-domain-12345.com",
    ]


# 性能测试夹具
@pytest.fixture
def performance_urls():
    """性能测试URL列表"""
    return [
        f"https://httpbin.org/delay/{i}"
        for i in range(1, 6)
    ]


# 标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "performance: 性能测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
