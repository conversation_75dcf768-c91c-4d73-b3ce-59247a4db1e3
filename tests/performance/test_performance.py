"""
性能测试

测试系统在各种负载下的性能表现。
"""

import pytest
import asyncio
import time
from statistics import mean, median
from unittest.mock import patch, AsyncMock

from mcp_web_fetcher.core.fetcher import WebFetcher
from mcp_web_fetcher.server import MCPServer
from mcp.types import CallToolRequest


@pytest.mark.performance
class TestPerformance:
    """性能测试类"""
    
    @pytest.mark.asyncio
    async def test_single_fetch_performance(self, web_fetcher):
        """测试单次抓取性能"""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟快速响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.headers = {"content-type": "text/html"}
            mock_response.text = AsyncMock(return_value="<html><body>Test</body></html>")
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # 测量单次抓取时间
            start_time = time.time()
            result = await web_fetcher.fetch_url("https://example.com")
            end_time = time.time()
            
            duration = end_time - start_time
            
            assert result.success is True
            assert duration < 1.0  # 应该在1秒内完成
    
    @pytest.mark.asyncio
    async def test_batch_fetch_performance(self, web_fetcher):
        """测试批量抓取性能"""
        urls = [f"https://example.com/page{i}" for i in range(10)]
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.headers = {"content-type": "text/html"}
            mock_response.text = AsyncMock(return_value="<html><body>Test</body></html>")
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # 测量批量抓取时间
            start_time = time.time()
            results = await web_fetcher.batch_fetch(urls)
            end_time = time.time()
            
            duration = end_time - start_time
            
            assert len(results) == 10
            assert all(result.success for result in results)
            assert duration < 5.0  # 10个URL应该在5秒内完成
            
            # 计算平均每个URL的处理时间
            avg_time_per_url = duration / len(urls)
            assert avg_time_per_url < 0.5  # 每个URL平均处理时间应小于0.5秒
    
    @pytest.mark.asyncio
    async def test_concurrent_performance(self, web_fetcher):
        """测试并发性能"""
        urls = [f"https://example.com/page{i}" for i in range(20)]
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.headers = {"content-type": "text/html"}
            mock_response.text = AsyncMock(return_value="<html><body>Test</body></html>")
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # 创建并发任务
            tasks = [web_fetcher.fetch_url(url) for url in urls]
            
            # 测量并发执行时间
            start_time = time.time()
            results = await asyncio.gather(*tasks)
            end_time = time.time()
            
            duration = end_time - start_time
            
            assert len(results) == 20
            assert all(result.success for result in results)
            
            # 并发执行应该比串行执行快得多
            # 假设串行执行每个需要0.1秒，20个需要2秒
            # 并发执行应该在1秒内完成
            assert duration < 2.0
    
    @pytest.mark.asyncio
    async def test_memory_usage(self, web_fetcher):
        """测试内存使用情况"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # 处理大量URL
        urls = [f"https://example.com/page{i}" for i in range(100)]
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟大内容响应
            large_content = "<html><body>" + "x" * 10000 + "</body></html>"
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.headers = {"content-type": "text/html"}
            mock_response.text = AsyncMock(return_value=large_content)
            mock_get.return_value.__aenter__.return_value = mock_response
            
            results = await web_fetcher.batch_fetch(urls)
            
            final_memory = process.memory_info().rss
            memory_increase = final_memory - initial_memory
            
            assert len(results) == 100
            # 内存增长应该在合理范围内 (小于100MB)
            assert memory_increase < 100 * 1024 * 1024
    
    @pytest.mark.asyncio
    async def test_response_time_distribution(self, web_fetcher):
        """测试响应时间分布"""
        urls = [f"https://example.com/page{i}" for i in range(50)]
        response_times = []
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟变化的响应时间
            async def mock_response_with_delay():
                await asyncio.sleep(0.01)  # 模拟10ms延迟
                mock_response = AsyncMock()
                mock_response.status = 200
                mock_response.headers = {"content-type": "text/html"}
                mock_response.text = AsyncMock(return_value="<html><body>Test</body></html>")
                return mock_response
            
            mock_get.return_value.__aenter__ = mock_response_with_delay
            
            # 测量每个请求的响应时间
            for url in urls:
                start_time = time.time()
                result = await web_fetcher.fetch_url(url)
                end_time = time.time()
                
                response_times.append(end_time - start_time)
                assert result.success is True
            
            # 分析响应时间分布
            avg_time = mean(response_times)
            median_time = median(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            
            assert avg_time < 0.1  # 平均响应时间应小于100ms
            assert median_time < 0.1  # 中位数响应时间应小于100ms
            assert max_time < 0.2  # 最大响应时间应小于200ms
            assert min_time > 0.005  # 最小响应时间应大于5ms (合理的网络延迟)
    
    @pytest.mark.asyncio
    async def test_throughput(self, mcp_server):
        """测试吞吐量"""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟快速响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.headers = {"content-type": "text/html"}
            mock_response.text = AsyncMock(return_value="<html><body>Test</body></html>")
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # 测试在1分钟内能处理多少请求
            start_time = time.time()
            request_count = 0
            duration_limit = 10  # 10秒测试
            
            while time.time() - start_time < duration_limit:
                request = CallToolRequest(
                    name="fetch_url",
                    arguments={
                        "url": f"https://example.com/page{request_count}",
                        "use_ml": False
                    }
                )
                
                result = await mcp_server._handle_fetch_url(request.arguments)
                assert result.isError is None or result.isError is False
                
                request_count += 1
            
            actual_duration = time.time() - start_time
            throughput = request_count / actual_duration
            
            # 期望吞吐量至少每秒10个请求
            assert throughput >= 10.0
            print(f"Throughput: {throughput:.2f} requests/second")
    
    @pytest.mark.asyncio
    async def test_error_rate_under_load(self, web_fetcher):
        """测试负载下的错误率"""
        urls = [f"https://example.com/page{i}" for i in range(100)]
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟部分失败的响应
            call_count = 0
            
            async def mock_response_with_failures():
                nonlocal call_count
                call_count += 1
                
                # 10%的请求失败
                if call_count % 10 == 0:
                    raise Exception("Simulated network error")
                
                mock_response = AsyncMock()
                mock_response.status = 200
                mock_response.headers = {"content-type": "text/html"}
                mock_response.text = AsyncMock(return_value="<html><body>Test</body></html>")
                return mock_response
            
            mock_get.return_value.__aenter__ = mock_response_with_failures
            
            results = await web_fetcher.batch_fetch(urls)
            
            success_count = sum(1 for result in results if result.success)
            error_count = len(results) - success_count
            error_rate = error_count / len(results)
            
            assert len(results) == 100
            # 错误率应该接近10%
            assert 0.08 <= error_rate <= 0.12
            print(f"Error rate: {error_rate:.2%}")


@pytest.mark.performance
@pytest.mark.slow
class TestStressTest:
    """压力测试"""
    
    @pytest.mark.asyncio
    async def test_high_concurrency(self, web_fetcher):
        """测试高并发处理"""
        # 创建大量并发请求
        urls = [f"https://example.com/page{i}" for i in range(200)]
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.headers = {"content-type": "text/html"}
            mock_response.text = AsyncMock(return_value="<html><body>Test</body></html>")
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # 分批处理以避免资源耗尽
            batch_size = 50
            all_results = []
            
            for i in range(0, len(urls), batch_size):
                batch_urls = urls[i:i + batch_size]
                batch_results = await web_fetcher.batch_fetch(batch_urls)
                all_results.extend(batch_results)
            
            assert len(all_results) == 200
            success_rate = sum(1 for result in all_results if result.success) / len(all_results)
            assert success_rate >= 0.95  # 至少95%成功率
    
    @pytest.mark.asyncio
    async def test_sustained_load(self, web_fetcher):
        """测试持续负载"""
        duration = 30  # 30秒持续测试
        start_time = time.time()
        total_requests = 0
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.headers = {"content-type": "text/html"}
            mock_response.text = AsyncMock(return_value="<html><body>Test</body></html>")
            mock_get.return_value.__aenter__.return_value = mock_response
            
            while time.time() - start_time < duration:
                # 每次处理10个URL
                urls = [f"https://example.com/page{i}" for i in range(10)]
                results = await web_fetcher.batch_fetch(urls)
                
                total_requests += len(results)
                success_count = sum(1 for result in results if result.success)
                
                # 确保成功率保持在高水平
                assert success_count >= 8  # 至少80%成功
                
                # 短暂休息避免过度负载
                await asyncio.sleep(0.1)
            
            actual_duration = time.time() - start_time
            avg_throughput = total_requests / actual_duration
            
            print(f"Sustained throughput: {avg_throughput:.2f} requests/second over {actual_duration:.1f} seconds")
            assert avg_throughput >= 5.0  # 至少每秒5个请求
