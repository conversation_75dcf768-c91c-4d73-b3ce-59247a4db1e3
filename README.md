# MCP Web Fetcher

一个基于机器学习的MCP服务，用于将网页内容转换为结构化数据，支持代理和批量处理。

## 🎯 项目特性

- 🌐 **智能网页抓取**: 支持静态和动态网页内容获取
- 🤖 **AI驱动解析**: 基于深度学习的内容理解和结构化提取
- 🔗 **代理支持**: 内置代理池管理，支持受限网站访问
- ⚡ **批量处理**: 高并发异步处理，支持大规模数据采集
- 🏗️ **模块化设计**: 松耦合架构，易于扩展和维护
- 📊 **可视化监控**: 完整的性能监控和日志追踪

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI应用客户端   │    │   MCP服务层     │    │   ML处理引擎    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Claude        │───▶│ • MCP Server    │───▶│ • 内容提取模型   │
│ • GPT           │    │ • 认证授权      │    │ • 布局分析模型   │
│ • 自定义AI      │    │ • 限流控制      │    │ • 内容分类模型   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   基础设施层     │    │   核心处理层     │    │   数据存储层    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • 代理池管理     │    │ • 网页抓取器     │    │ • PostgreSQL    │
│ • Redis缓存     │    │ • 内容解析器     │    │ • Redis缓存     │
│ • 消息队列      │    │ • 结构化转换器   │    │ • 文件存储      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.11+
- Docker & Docker Compose
- Redis
- PostgreSQL

### 安装部署

```bash
# 克隆项目
git clone https://github.com/your-org/mcp-web-fetcher.git
cd mcp-web-fetcher

# 安装依赖
pip install -r requirements.txt

# 启动服务
docker-compose up -d

# 运行MCP服务
python -m mcp_web_fetcher.server
```

## 📚 API文档

### MCP工具接口

#### 1. fetch_url - 单URL抓取
```json
{
  "name": "fetch_url",
  "arguments": {
    "url": "https://example.com",
    "format": "json",
    "use_proxy": true
  }
}
```

#### 2. batch_fetch - 批量抓取
```json
{
  "name": "batch_fetch",
  "arguments": {
    "urls": ["https://example1.com", "https://example2.com"],
    "format": "json",
    "max_concurrent": 10
  }
}
```

## 🧠 ML模型

### 支持的模型类型
- **内容提取**: BERT + BiLSTM + CRF
- **布局分析**: Vision Transformer + CNN  
- **内容分类**: RoBERTa + 多标签分类
- **内容清洗**: Transformer + 序列标注

### 模型性能
- 内容提取准确率: >90%
- 处理速度: <5秒/页面
- 支持语言: 中文、英文、多语言

## 📊 监控指标

- 请求成功率
- 平均响应时间
- 并发处理能力
- 模型推理性能
- 资源使用情况

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

MIT License
