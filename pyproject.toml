[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mcp-web-fetcher"
version = "0.1.0"
description = "基于机器学习的MCP服务，用于将网页内容转换为结构化数据"
authors = [
    {name = "MCP Web Fetcher Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: Indexing/Search",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
keywords = ["mcp", "web-scraping", "machine-learning", "ai", "nlp"]

dependencies = [
    # MCP相关
    "mcp>=1.0.0",
    
    # Web框架
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "httpx>=0.25.0",
    
    # 网页抓取
    "playwright>=1.40.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "requests>=2.31.0",
    "aiohttp>=3.9.0",
    
    # 机器学习
    "torch>=2.1.0",
    "transformers>=4.35.0",
    "sentence-transformers>=2.2.0",
    "spacy>=3.7.0",
    "scikit-learn>=1.3.0",
    "numpy>=1.24.0",
    "pandas>=2.1.0",
    
    # 数据库和缓存
    "asyncpg>=0.29.0",
    "redis>=5.0.0",
    "sqlalchemy[asyncio]>=2.0.0",
    "alembic>=1.12.0",
    
    # 任务队列
    "celery>=5.3.0",
    "kombu>=5.3.0",
    
    # 配置和日志
    "pydantic-settings>=2.1.0",
    "structlog>=23.2.0",
    "rich>=13.7.0",
    
    # 监控和追踪
    "prometheus-client>=0.19.0",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
    
    # 工具库
    "click>=8.1.0",
    "python-multipart>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
]

gpu = [
    "torch[cuda]>=2.1.0",
    "torchvision>=0.16.0",
]

[project.urls]
Homepage = "https://github.com/your-org/mcp-web-fetcher"
Repository = "https://github.com/your-org/mcp-web-fetcher.git"
Documentation = "https://mcp-web-fetcher.readthedocs.io"
"Bug Tracker" = "https://github.com/your-org/mcp-web-fetcher/issues"

[project.scripts]
mcp-web-fetcher = "mcp_web_fetcher.cli:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["mcp_web_fetcher"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "playwright.*",
    "celery.*",
    "redis.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=mcp_web_fetcher",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src/mcp_web_fetcher"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
