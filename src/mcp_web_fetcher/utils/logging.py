"""
日志配置模块

配置结构化日志记录。
"""

import logging
import sys
from pathlib import Path
from typing import Optional

import structlog
from rich.logging import RichHandler

from ..config import settings


def setup_logging(log_level: Optional[str] = None):
    """设置日志配置
    
    Args:
        log_level: 日志级别，如果不指定则使用配置文件中的设置
    """
    # 确定日志级别
    level = log_level or settings.logging.level
    
    # 创建日志目录
    if settings.logging.file_enabled:
        log_file = Path(settings.logging.file_path)
        log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 配置标准库日志
    logging.basicConfig(
        level=getattr(logging, level),
        format=settings.logging.format,
        handlers=_get_handlers()
    )
    
    # 配置structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # 设置第三方库日志级别
    _configure_third_party_loggers()


def _get_handlers():
    """获取日志处理器"""
    handlers = []
    
    # 控制台处理器 (使用Rich美化输出)
    console_handler = RichHandler(
        rich_tracebacks=True,
        show_time=True,
        show_path=True
    )
    console_handler.setLevel(logging.INFO)
    handlers.append(console_handler)
    
    # 文件处理器
    if settings.logging.file_enabled:
        file_handler = logging.FileHandler(
            settings.logging.file_path,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        handlers.append(file_handler)
    
    return handlers


def _configure_third_party_loggers():
    """配置第三方库的日志级别"""
    # 设置第三方库日志级别，避免过多噪音
    third_party_loggers = {
        'urllib3': logging.WARNING,
        'requests': logging.WARNING,
        'aiohttp': logging.WARNING,
        'asyncio': logging.WARNING,
        'playwright': logging.WARNING,
        'transformers': logging.WARNING,
        'torch': logging.WARNING,
        'redis': logging.WARNING,
        'sqlalchemy': logging.WARNING,
    }
    
    for logger_name, level in third_party_loggers.items():
        logging.getLogger(logger_name).setLevel(level)


def get_logger(name: str) -> structlog.BoundLogger:
    """获取结构化日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        structlog.BoundLogger: 结构化日志记录器
    """
    return structlog.get_logger(name)
