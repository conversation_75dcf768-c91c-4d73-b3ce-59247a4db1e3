"""
自定义异常类

定义项目中使用的各种异常类型。
"""


class MCPWebFetcherError(Exception):
    """基础异常类"""
    pass


class FetchError(MCPWebFetcherError):
    """网页抓取异常"""
    pass


class TimeoutError(FetchError):
    """超时异常"""
    pass


class ProxyError(FetchError):
    """代理异常"""
    pass


class MLError(MCPWebFetcherError):
    """机器学习处理异常"""
    pass


class ConfigError(MCPWebFetcherError):
    """配置异常"""
    pass


class CacheError(MCPWebFetcherError):
    """缓存异常"""
    pass
