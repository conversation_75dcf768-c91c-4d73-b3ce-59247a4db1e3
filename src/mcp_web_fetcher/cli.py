"""
命令行接口

提供MCP Web Fetcher的命令行工具。
"""

import asyncio
import click
import json
from pathlib import Path
from typing import List, Optional

from .server import MCPServer
from .config import settings
from .utils.logging import setup_logging


@click.group()
@click.option('--debug', is_flag=True, help='启用调试模式')
@click.option('--config', type=click.Path(exists=True), help='配置文件路径')
def main(debug: bool, config: Optional[str]):
    """MCP Web Fetcher 命令行工具"""
    if debug:
        settings.logging.level = "DEBUG"
        settings.mcp.debug = True
    
    if config:
        # 加载自定义配置文件
        click.echo(f"Loading config from: {config}")
    
    setup_logging()


@main.command()
@click.option('--host', default='localhost', help='服务器主机')
@click.option('--port', default=8000, help='服务器端口')
def serve(host: str, port: int):
    """启动MCP服务器"""
    click.echo(f"Starting MCP Web Fetcher server on {host}:{port}")
    
    # 更新配置
    settings.mcp.host = host
    settings.mcp.port = port
    
    # 运行服务器
    asyncio.run(run_server())


@main.command()
@click.argument('url')
@click.option('--format', 'output_format', default='json', 
              type=click.Choice(['json', 'markdown', 'text']),
              help='输出格式')
@click.option('--output', '-o', type=click.Path(), help='输出文件路径')
@click.option('--proxy', is_flag=True, help='使用代理')
@click.option('--no-cache', is_flag=True, help='禁用缓存')
@click.option('--no-ml', is_flag=True, help='禁用ML处理')
def fetch(url: str, output_format: str, output: Optional[str], 
          proxy: bool, no_cache: bool, no_ml: bool):
    """抓取单个URL"""
    click.echo(f"Fetching: {url}")
    
    result = asyncio.run(fetch_url_cli(
        url=url,
        format_type=output_format,
        use_proxy=proxy,
        use_cache=not no_cache,
        use_ml=not no_ml
    ))
    
    if output:
        Path(output).write_text(result, encoding='utf-8')
        click.echo(f"Result saved to: {output}")
    else:
        click.echo(result)


@main.command()
@click.argument('urls', nargs=-1, required=True)
@click.option('--format', 'output_format', default='json',
              type=click.Choice(['json', 'markdown', 'text']),
              help='输出格式')
@click.option('--output', '-o', type=click.Path(), help='输出文件路径')
@click.option('--proxy', is_flag=True, help='使用代理')
@click.option('--no-cache', is_flag=True, help='禁用缓存')
@click.option('--no-ml', is_flag=True, help='禁用ML处理')
@click.option('--concurrent', default=10, help='最大并发数')
def batch(urls: List[str], output_format: str, output: Optional[str],
          proxy: bool, no_cache: bool, no_ml: bool, concurrent: int):
    """批量抓取URL"""
    click.echo(f"Batch fetching {len(urls)} URLs")
    
    result = asyncio.run(batch_fetch_cli(
        urls=list(urls),
        format_type=output_format,
        use_proxy=proxy,
        use_cache=not no_cache,
        use_ml=not no_ml,
        max_concurrent=concurrent
    ))
    
    if output:
        Path(output).write_text(result, encoding='utf-8')
        click.echo(f"Results saved to: {output}")
    else:
        click.echo(result)


@main.command()
def stats():
    """显示服务统计信息"""
    result = asyncio.run(get_stats_cli())
    click.echo(result)


@main.command()
@click.option('--pattern', help='清理模式')
def clear_cache(pattern: Optional[str]):
    """清理缓存"""
    asyncio.run(clear_cache_cli(pattern))
    if pattern:
        click.echo(f"Cache cleared with pattern: {pattern}")
    else:
        click.echo("All cache cleared")


@main.command()
def test():
    """运行测试"""
    import subprocess
    import sys
    
    click.echo("Running tests...")
    result = subprocess.run([sys.executable, '-m', 'pytest', 'tests/'], 
                          capture_output=True, text=True)
    
    click.echo(result.stdout)
    if result.stderr:
        click.echo(result.stderr, err=True)
    
    sys.exit(result.returncode)


@main.command()
@click.option('--check', is_flag=True, help='只检查，不修复')
def lint(check: bool):
    """代码格式检查和修复"""
    import subprocess
    import sys
    
    commands = [
        ['black', '--check' if check else '', 'src/', 'tests/'],
        ['isort', '--check-only' if check else '', 'src/', 'tests/'],
        ['flake8', 'src/', 'tests/']
    ]
    
    for cmd in commands:
        cmd = [c for c in cmd if c]  # 移除空字符串
        click.echo(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd)
        if result.returncode != 0:
            sys.exit(result.returncode)


async def run_server():
    """运行MCP服务器"""
    from .server import main as server_main
    await server_main()


async def fetch_url_cli(url: str, format_type: str, use_proxy: bool, 
                       use_cache: bool, use_ml: bool) -> str:
    """CLI版本的URL抓取"""
    server = MCPServer()
    
    try:
        await server.initialize()
        
        result = await server._handle_fetch_url({
            'url': url,
            'format': format_type,
            'use_proxy': use_proxy,
            'use_cache': use_cache,
            'use_ml': use_ml
        })
        
        return result.content[0].text
        
    finally:
        await server.cleanup()


async def batch_fetch_cli(urls: List[str], format_type: str, use_proxy: bool,
                         use_cache: bool, use_ml: bool, max_concurrent: int) -> str:
    """CLI版本的批量抓取"""
    server = MCPServer()
    
    try:
        await server.initialize()
        
        result = await server._handle_batch_fetch({
            'urls': urls,
            'format': format_type,
            'use_proxy': use_proxy,
            'use_cache': use_cache,
            'use_ml': use_ml,
            'max_concurrent': max_concurrent
        })
        
        return result.content[0].text
        
    finally:
        await server.cleanup()


async def get_stats_cli() -> str:
    """CLI版本的统计信息获取"""
    server = MCPServer()
    
    try:
        await server.initialize()
        
        result = await server._handle_get_stats({})
        return result.content[0].text
        
    finally:
        await server.cleanup()


async def clear_cache_cli(pattern: Optional[str]):
    """CLI版本的缓存清理"""
    server = MCPServer()
    
    try:
        await server.initialize()
        await server._handle_clear_cache({'pattern': pattern} if pattern else {})
        
    finally:
        await server.cleanup()


if __name__ == '__main__':
    main()
