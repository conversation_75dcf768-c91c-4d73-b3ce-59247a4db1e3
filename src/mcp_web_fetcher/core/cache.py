"""
缓存管理模块

负责抓取结果的缓存管理，支持内存和Redis缓存。
"""

import json
import logging
import hashlib
from typing import Optional, Any, Dict
from datetime import datetime, timedelta

import redis.asyncio as redis
from redis.asyncio import Redis

from ..config import settings
from .fetcher import FetchResult


class CacheManager:
    """缓存管理器
    
    支持多层缓存策略:
    - L1: 内存缓存 (快速访问)
    - L2: Redis缓存 (持久化)
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 内存缓存
        self._memory_cache: Dict[str, Any] = {}
        self._memory_cache_timestamps: Dict[str, datetime] = {}
        
        # Redis缓存
        self._redis: Optional[Redis] = None
        
        # 配置参数
        self.enabled = settings.fetcher.cache_enabled
        self.ttl = settings.fetcher.cache_ttl
        self.max_memory_items = 1000  # 内存缓存最大条目数
        
        if self.enabled:
            self._init_redis()
    
    def _init_redis(self):
        """初始化Redis连接"""
        try:
            self._redis = redis.from_url(
                settings.redis.url,
                encoding="utf-8",
                decode_responses=True
            )
            self.logger.info("Redis cache initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize Redis cache: {e}")
            self._redis = None
    
    def _generate_cache_key(self, url: str, **kwargs) -> str:
        """生成缓存键"""
        # 将URL和参数组合生成唯一键
        key_data = {"url": url, **kwargs}
        key_string = json.dumps(key_data, sort_keys=True)
        
        # 使用MD5生成短键
        return hashlib.md5(key_string.encode()).hexdigest()
    
    async def get(self, url: str, **kwargs) -> Optional[FetchResult]:
        """获取缓存结果
        
        Args:
            url: 目标URL
            **kwargs: 额外参数
            
        Returns:
            Optional[FetchResult]: 缓存的结果，如果不存在则返回None
        """
        if not self.enabled:
            return None
        
        cache_key = self._generate_cache_key(url, **kwargs)
        
        # 先检查内存缓存
        result = self._get_from_memory(cache_key)
        if result:
            self.logger.debug(f"Memory cache hit for key: {cache_key}")
            return result
        
        # 再检查Redis缓存
        result = await self._get_from_redis(cache_key)
        if result:
            self.logger.debug(f"Redis cache hit for key: {cache_key}")
            # 将结果放入内存缓存
            self._set_to_memory(cache_key, result)
            return result
        
        self.logger.debug(f"Cache miss for key: {cache_key}")
        return None
    
    async def set(self, url: str, result: FetchResult, **kwargs):
        """设置缓存结果
        
        Args:
            url: 目标URL
            result: 抓取结果
            **kwargs: 额外参数
        """
        if not self.enabled or not result.success:
            return
        
        cache_key = self._generate_cache_key(url, **kwargs)
        
        # 设置内存缓存
        self._set_to_memory(cache_key, result)
        
        # 设置Redis缓存
        await self._set_to_redis(cache_key, result)
        
        self.logger.debug(f"Cached result for key: {cache_key}")
    
    def _get_from_memory(self, cache_key: str) -> Optional[FetchResult]:
        """从内存缓存获取"""
        if cache_key not in self._memory_cache:
            return None
        
        # 检查是否过期
        timestamp = self._memory_cache_timestamps.get(cache_key)
        if timestamp and datetime.now() - timestamp > timedelta(seconds=self.ttl):
            self._remove_from_memory(cache_key)
            return None
        
        return self._memory_cache[cache_key]
    
    def _set_to_memory(self, cache_key: str, result: FetchResult):
        """设置内存缓存"""
        # 检查缓存大小限制
        if len(self._memory_cache) >= self.max_memory_items:
            self._evict_memory_cache()
        
        self._memory_cache[cache_key] = result
        self._memory_cache_timestamps[cache_key] = datetime.now()
    
    def _remove_from_memory(self, cache_key: str):
        """从内存缓存移除"""
        self._memory_cache.pop(cache_key, None)
        self._memory_cache_timestamps.pop(cache_key, None)
    
    def _evict_memory_cache(self):
        """清理内存缓存 (LRU策略)"""
        if not self._memory_cache_timestamps:
            return
        
        # 找到最旧的条目
        oldest_key = min(
            self._memory_cache_timestamps.keys(),
            key=lambda k: self._memory_cache_timestamps[k]
        )
        
        self._remove_from_memory(oldest_key)
        self.logger.debug(f"Evicted memory cache entry: {oldest_key}")
    
    async def _get_from_redis(self, cache_key: str) -> Optional[FetchResult]:
        """从Redis缓存获取"""
        if not self._redis:
            return None
        
        try:
            data = await self._redis.get(f"fetch:{cache_key}")
            if data:
                result_dict = json.loads(data)
                return self._deserialize_fetch_result(result_dict)
        except Exception as e:
            self.logger.error(f"Failed to get from Redis cache: {e}")
        
        return None
    
    async def _set_to_redis(self, cache_key: str, result: FetchResult):
        """设置Redis缓存"""
        if not self._redis:
            return
        
        try:
            data = json.dumps(self._serialize_fetch_result(result))
            await self._redis.setex(
                f"fetch:{cache_key}",
                self.ttl,
                data
            )
        except Exception as e:
            self.logger.error(f"Failed to set Redis cache: {e}")
    
    def _serialize_fetch_result(self, result: FetchResult) -> Dict:
        """序列化FetchResult"""
        return {
            "url": result.url,
            "content": result.content,
            "status_code": result.status_code,
            "headers": result.headers,
            "metadata": result.metadata,
            "timestamp": result.timestamp.isoformat(),
            "success": result.success,
            "error": result.error
        }
    
    def _deserialize_fetch_result(self, data: Dict) -> FetchResult:
        """反序列化FetchResult"""
        return FetchResult(
            url=data["url"],
            content=data["content"],
            status_code=data["status_code"],
            headers=data["headers"],
            metadata=data["metadata"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            success=data["success"],
            error=data.get("error")
        )
    
    async def clear(self, pattern: Optional[str] = None):
        """清理缓存
        
        Args:
            pattern: 清理模式，如果为None则清理所有缓存
        """
        # 清理内存缓存
        if pattern is None:
            self._memory_cache.clear()
            self._memory_cache_timestamps.clear()
        else:
            keys_to_remove = [
                key for key in self._memory_cache.keys()
                if pattern in key
            ]
            for key in keys_to_remove:
                self._remove_from_memory(key)
        
        # 清理Redis缓存
        if self._redis:
            try:
                if pattern is None:
                    # 清理所有fetch相关的键
                    keys = await self._redis.keys("fetch:*")
                else:
                    keys = await self._redis.keys(f"fetch:*{pattern}*")
                
                if keys:
                    await self._redis.delete(*keys)
                    self.logger.info(f"Cleared {len(keys)} Redis cache entries")
            except Exception as e:
                self.logger.error(f"Failed to clear Redis cache: {e}")
    
    async def get_stats(self) -> Dict:
        """获取缓存统计信息"""
        stats = {
            "enabled": self.enabled,
            "memory_cache": {
                "size": len(self._memory_cache),
                "max_size": self.max_memory_items
            },
            "redis_cache": {
                "connected": self._redis is not None
            },
            "ttl": self.ttl
        }
        
        if self._redis:
            try:
                info = await self._redis.info("memory")
                stats["redis_cache"]["memory_usage"] = info.get("used_memory_human", "unknown")
                
                # 统计fetch相关的键数量
                keys = await self._redis.keys("fetch:*")
                stats["redis_cache"]["key_count"] = len(keys)
            except Exception as e:
                self.logger.error(f"Failed to get Redis stats: {e}")
                stats["redis_cache"]["error"] = str(e)
        
        return stats
    
    async def cleanup(self):
        """清理资源"""
        if self._redis:
            await self._redis.close()
