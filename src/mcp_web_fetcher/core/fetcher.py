"""
网页抓取器模块

负责从指定URL获取网页内容，支持静态和动态页面抓取。
"""

import asyncio
import logging
from typing import Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass
from datetime import datetime

import aiohttp
from playwright.async_api import async_playwright, <PERSON>rows<PERSON>, Page
from bs4 import BeautifulSoup

from ..config import settings
from .proxy import ProxyManager
from .cache import CacheManager
from ..utils.exceptions import FetchError, TimeoutError, ProxyError


@dataclass
class FetchResult:
    """抓取结果数据类"""
    
    url: str
    content: str
    status_code: int
    headers: Dict[str, str]
    metadata: Dict[str, Union[str, int, float]]
    timestamp: datetime
    success: bool
    error: Optional[str] = None


class WebFetcher:
    """网页抓取器
    
    支持静态和动态网页抓取，具备以下特性:
    - 多种抓取方式 (requests, aiohttp, playwright)
    - 代理支持和轮换
    - 智能重试机制
    - 缓存管理
    - 并发控制
    """
    
    def __init__(
        self,
        proxy_manager: Optional[ProxyManager] = None,
        cache_manager: Optional[CacheManager] = None,
    ):
        self.logger = logging.getLogger(__name__)
        self.proxy_manager = proxy_manager or ProxyManager()
        self.cache_manager = cache_manager or CacheManager()
        
        # 配置参数
        self.max_concurrent = settings.fetcher.max_concurrent
        self.timeout = settings.fetcher.timeout
        self.retry_times = settings.fetcher.retry_times
        self.retry_delay = settings.fetcher.retry_delay
        self.user_agent = settings.fetcher.user_agent
        
        # 并发控制
        self._semaphore = asyncio.Semaphore(self.max_concurrent)
        self._session: Optional[aiohttp.ClientSession] = None
        self._browser: Optional[Browser] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._init_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self._cleanup()
    
    async def _init_session(self):
        """初始化HTTP会话"""
        if self._session is None:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            headers = {"User-Agent": self.user_agent}
            self._session = aiohttp.ClientSession(
                timeout=timeout,
                headers=headers
            )
    
    async def _cleanup(self):
        """清理资源"""
        if self._session:
            await self._session.close()
            self._session = None
        
        if self._browser:
            await self._browser.close()
            self._browser = None
    
    async def fetch_url(
        self,
        url: str,
        method: str = "auto",
        use_proxy: bool = False,
        use_cache: bool = True,
        **kwargs
    ) -> FetchResult:
        """抓取单个URL
        
        Args:
            url: 目标URL
            method: 抓取方法 ("auto", "static", "dynamic")
            use_proxy: 是否使用代理
            use_cache: 是否使用缓存
            **kwargs: 额外参数
            
        Returns:
            FetchResult: 抓取结果
        """
        async with self._semaphore:
            return await self._fetch_with_retry(
                url, method, use_proxy, use_cache, **kwargs
            )
    
    async def batch_fetch(
        self,
        urls: List[str],
        method: str = "auto",
        use_proxy: bool = False,
        use_cache: bool = True,
        **kwargs
    ) -> List[FetchResult]:
        """批量抓取URL
        
        Args:
            urls: URL列表
            method: 抓取方法
            use_proxy: 是否使用代理
            use_cache: 是否使用缓存
            **kwargs: 额外参数
            
        Returns:
            List[FetchResult]: 抓取结果列表
        """
        tasks = [
            self.fetch_url(url, method, use_proxy, use_cache, **kwargs)
            for url in urls
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(
                    FetchResult(
                        url=urls[i],
                        content="",
                        status_code=0,
                        headers={},
                        metadata={},
                        timestamp=datetime.now(),
                        success=False,
                        error=str(result)
                    )
                )
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _fetch_with_retry(
        self,
        url: str,
        method: str,
        use_proxy: bool,
        use_cache: bool,
        **kwargs
    ) -> FetchResult:
        """带重试的抓取"""
        last_error = None
        
        for attempt in range(self.retry_times + 1):
            try:
                # 检查缓存
                if use_cache and attempt == 0:
                    cached_result = await self.cache_manager.get(url)
                    if cached_result:
                        self.logger.debug(f"Cache hit for URL: {url}")
                        return cached_result
                
                # 执行抓取
                result = await self._do_fetch(url, method, use_proxy, **kwargs)
                
                # 缓存结果
                if use_cache and result.success:
                    await self.cache_manager.set(url, result)
                
                return result
                
            except Exception as e:
                last_error = e
                self.logger.warning(
                    f"Fetch attempt {attempt + 1} failed for {url}: {e}"
                )
                
                if attempt < self.retry_times:
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))
        
        # 所有重试都失败
        return FetchResult(
            url=url,
            content="",
            status_code=0,
            headers={},
            metadata={},
            timestamp=datetime.now(),
            success=False,
            error=str(last_error)
        )
    
    async def _do_fetch(
        self,
        url: str,
        method: str,
        use_proxy: bool,
        **kwargs
    ) -> FetchResult:
        """执行实际的抓取操作"""
        # 自动选择抓取方法
        if method == "auto":
            method = self._detect_fetch_method(url)
        
        if method == "dynamic":
            return await self._fetch_dynamic(url, use_proxy, **kwargs)
        else:
            return await self._fetch_static(url, use_proxy, **kwargs)
    
    def _detect_fetch_method(self, url: str) -> str:
        """检测抓取方法
        
        基于URL特征判断是否需要动态抓取
        """
        # 简单的启发式规则
        dynamic_indicators = [
            "javascript:",
            "#",
            "spa",
            "react",
            "vue",
            "angular"
        ]
        
        url_lower = url.lower()
        for indicator in dynamic_indicators:
            if indicator in url_lower:
                return "dynamic"
        
        return "static"
    
    async def _fetch_static(
        self,
        url: str,
        use_proxy: bool,
        **kwargs
    ) -> FetchResult:
        """静态页面抓取"""
        await self._init_session()
        
        proxy = None
        if use_proxy:
            proxy = await self.proxy_manager.get_proxy()
        
        try:
            async with self._session.get(url, proxy=proxy, **kwargs) as response:
                content = await response.text()
                
                return FetchResult(
                    url=url,
                    content=content,
                    status_code=response.status,
                    headers=dict(response.headers),
                    metadata={
                        "method": "static",
                        "proxy": proxy,
                        "content_length": len(content),
                        "content_type": response.headers.get("content-type", "")
                    },
                    timestamp=datetime.now(),
                    success=response.status == 200
                )
                
        except Exception as e:
            raise FetchError(f"Static fetch failed for {url}: {e}")
    
    async def _fetch_dynamic(
        self,
        url: str,
        use_proxy: bool,
        **kwargs
    ) -> FetchResult:
        """动态页面抓取"""
        if self._browser is None:
            playwright = await async_playwright().start()
            self._browser = await playwright.chromium.launch(
                headless=settings.fetcher.headless
            )
        
        context_options = {
            "user_agent": self.user_agent,
        }
        
        if use_proxy:
            proxy = await self.proxy_manager.get_proxy()
            if proxy:
                context_options["proxy"] = {"server": proxy}
        
        context = await self._browser.new_context(**context_options)
        page = await context.new_page()
        
        try:
            response = await page.goto(url, timeout=self.timeout * 1000)
            
            # 等待页面加载完成
            await page.wait_for_load_state("networkidle")
            
            content = await page.content()
            
            return FetchResult(
                url=url,
                content=content,
                status_code=response.status if response else 0,
                headers=dict(response.headers) if response else {},
                metadata={
                    "method": "dynamic",
                    "proxy": context_options.get("proxy"),
                    "content_length": len(content),
                    "page_title": await page.title()
                },
                timestamp=datetime.now(),
                success=response.status == 200 if response else False
            )
            
        except Exception as e:
            raise FetchError(f"Dynamic fetch failed for {url}: {e}")
        
        finally:
            await context.close()
    
    def extract_links(self, content: str, base_url: str) -> List[str]:
        """从页面内容中提取链接"""
        soup = BeautifulSoup(content, 'html.parser')
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            absolute_url = urljoin(base_url, href)
            links.append(absolute_url)
        
        return links
