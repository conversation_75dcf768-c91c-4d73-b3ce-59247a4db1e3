"""
结构化转换器模块

负责将处理后的内容转换为各种结构化格式。
"""

import json
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path

import yaml
from jinja2 import Environment, FileSystemLoader, Template

from ..config import settings
from ..utils.exceptions import MLError


class StructureConverter:
    """结构化转换器
    
    功能特性:
    - 多格式输出支持 (JSON, XML, YAML, Markdown)
    - 自定义模板引擎
    - 数据验证和清洗
    - 增量更新支持
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 初始化Jinja2环境
        template_dir = Path(__file__).parent.parent / "templates"
        if template_dir.exists():
            self.jinja_env = Environment(loader=FileSystemLoader(str(template_dir)))
        else:
            self.jinja_env = Environment()
        
        # 注册自定义过滤器
        self._register_filters()
    
    def convert(
        self,
        data: Dict[str, Any],
        format_type: str = "json",
        template: Optional[str] = None,
        **kwargs
    ) -> str:
        """转换数据为指定格式
        
        Args:
            data: 要转换的数据
            format_type: 输出格式 (json, xml, yaml, markdown, html)
            template: 自定义模板名称
            **kwargs: 额外参数
            
        Returns:
            str: 转换后的字符串
        """
        try:
            # 数据预处理
            processed_data = self._preprocess_data(data)
            
            # 根据格式类型转换
            if template:
                return self._convert_with_template(processed_data, template, **kwargs)
            elif format_type.lower() == "json":
                return self._to_json(processed_data, **kwargs)
            elif format_type.lower() == "xml":
                return self._to_xml(processed_data, **kwargs)
            elif format_type.lower() == "yaml":
                return self._to_yaml(processed_data, **kwargs)
            elif format_type.lower() == "markdown":
                return self._to_markdown(processed_data, **kwargs)
            elif format_type.lower() == "html":
                return self._to_html(processed_data, **kwargs)
            else:
                raise ValueError(f"Unsupported format: {format_type}")
                
        except Exception as e:
            self.logger.error(f"Conversion failed: {e}")
            raise MLError(f"Data conversion failed: {e}")
    
    def _preprocess_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """预处理数据"""
        processed = data.copy()
        
        # 添加元数据
        processed['_metadata'] = {
            'converted_at': datetime.now().isoformat(),
            'converter_version': '1.0.0'
        }
        
        # 清理None值
        processed = self._clean_none_values(processed)
        
        # 验证数据结构
        self._validate_data(processed)
        
        return processed
    
    def _clean_none_values(self, data: Any) -> Any:
        """递归清理None值"""
        if isinstance(data, dict):
            return {k: self._clean_none_values(v) for k, v in data.items() if v is not None}
        elif isinstance(data, list):
            return [self._clean_none_values(item) for item in data if item is not None]
        else:
            return data
    
    def _validate_data(self, data: Dict[str, Any]):
        """验证数据结构"""
        required_fields = ['url']
        
        for field in required_fields:
            if field not in data:
                self.logger.warning(f"Missing required field: {field}")
    
    def _to_json(self, data: Dict[str, Any], **kwargs) -> str:
        """转换为JSON格式"""
        indent = kwargs.get('indent', 2)
        ensure_ascii = kwargs.get('ensure_ascii', False)
        sort_keys = kwargs.get('sort_keys', False)
        
        return json.dumps(
            data,
            indent=indent,
            ensure_ascii=ensure_ascii,
            sort_keys=sort_keys,
            default=self._json_serializer
        )
    
    def _json_serializer(self, obj):
        """JSON序列化器"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)
    
    def _to_xml(self, data: Dict[str, Any], **kwargs) -> str:
        """转换为XML格式"""
        root_tag = kwargs.get('root_tag', 'document')
        
        def dict_to_xml(d, parent_tag='item'):
            xml_parts = [f'<{parent_tag}>']
            
            for key, value in d.items():
                # 清理标签名
                clean_key = self._clean_xml_tag(key)
                
                if isinstance(value, dict):
                    xml_parts.append(dict_to_xml(value, clean_key))
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            xml_parts.append(dict_to_xml(item, clean_key))
                        else:
                            xml_parts.append(f'<{clean_key}>{self._escape_xml(str(item))}</{clean_key}>')
                else:
                    xml_parts.append(f'<{clean_key}>{self._escape_xml(str(value))}</{clean_key}>')
            
            xml_parts.append(f'</{parent_tag}>')
            return '\n'.join(xml_parts)
        
        xml_content = dict_to_xml(data, root_tag)
        return f'<?xml version="1.0" encoding="UTF-8"?>\n{xml_content}'
    
    def _clean_xml_tag(self, tag: str) -> str:
        """清理XML标签名"""
        import re
        # 移除非法字符，替换为下划线
        clean_tag = re.sub(r'[^a-zA-Z0-9_-]', '_', str(tag))
        # 确保以字母开头
        if clean_tag and not clean_tag[0].isalpha():
            clean_tag = 'tag_' + clean_tag
        return clean_tag or 'item'
    
    def _escape_xml(self, text: str) -> str:
        """转义XML特殊字符"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&apos;'))
    
    def _to_yaml(self, data: Dict[str, Any], **kwargs) -> str:
        """转换为YAML格式"""
        default_flow_style = kwargs.get('default_flow_style', False)
        allow_unicode = kwargs.get('allow_unicode', True)
        
        return yaml.dump(
            data,
            default_flow_style=default_flow_style,
            allow_unicode=allow_unicode,
            sort_keys=False
        )
    
    def _to_markdown(self, data: Dict[str, Any], **kwargs) -> str:
        """转换为Markdown格式"""
        md_parts = []
        
        # 标题
        title = data.get('title', 'Untitled Document')
        md_parts.append(f"# {title}\n")
        
        # 基本信息
        url = data.get('url', '')
        if url:
            md_parts.append(f"**URL:** {url}\n")
        
        # 元数据
        meta = data.get('meta', {})
        if meta:
            md_parts.append("## 元数据\n")
            for key, value in meta.items():
                if isinstance(value, str) and len(value) < 200:
                    md_parts.append(f"- **{key}:** {value}")
            md_parts.append("")
        
        # 主要内容
        content = data.get('content', {})
        if content:
            md_parts.append("## 内容\n")
            
            # 提取的内容
            extracted = content.get('extracted_content', {})
            if extracted:
                main_content = extracted.get('main_content', '')
                if main_content:
                    md_parts.append(main_content[:1000] + "..." if len(main_content) > 1000 else main_content)
                    md_parts.append("")
        
        # 链接
        links = data.get('links', [])
        if links:
            md_parts.append("## 链接\n")
            for link in links[:10]:  # 限制显示数量
                text = link.get('text', 'Link')
                url = link.get('url', '')
                md_parts.append(f"- [{text}]({url})")
            md_parts.append("")
        
        # 图片
        images = data.get('images', [])
        if images:
            md_parts.append("## 图片\n")
            for img in images[:5]:  # 限制显示数量
                alt = img.get('alt', 'Image')
                url = img.get('url', '')
                md_parts.append(f"![{alt}]({url})")
            md_parts.append("")
        
        # 分类信息
        classification = data.get('classification', {})
        if classification:
            md_parts.append("## 分类\n")
            primary = classification.get('primary_category', '')
            if primary:
                md_parts.append(f"**主要分类:** {primary}\n")
        
        return "\n".join(md_parts)
    
    def _to_html(self, data: Dict[str, Any], **kwargs) -> str:
        """转换为HTML格式"""
        template_name = kwargs.get('template', 'default.html')
        
        try:
            template = self.jinja_env.get_template(template_name)
            return template.render(data=data, **kwargs)
        except Exception:
            # 使用默认HTML模板
            return self._default_html_template(data)
    
    def _default_html_template(self, data: Dict[str, Any]) -> str:
        """默认HTML模板"""
        title = data.get('title', 'Untitled Document')
        url = data.get('url', '')
        
        html_parts = [
            "<!DOCTYPE html>",
            "<html>",
            "<head>",
            f"<title>{title}</title>",
            "<meta charset='utf-8'>",
            "<style>",
            "body { font-family: Arial, sans-serif; margin: 40px; }",
            "h1 { color: #333; }",
            "h2 { color: #666; border-bottom: 1px solid #eee; }",
            ".meta { background: #f5f5f5; padding: 10px; margin: 10px 0; }",
            ".content { line-height: 1.6; }",
            "</style>",
            "</head>",
            "<body>",
            f"<h1>{title}</h1>",
        ]
        
        if url:
            html_parts.append(f"<p><strong>URL:</strong> <a href='{url}'>{url}</a></p>")
        
        # 元数据
        meta = data.get('meta', {})
        if meta:
            html_parts.append("<h2>元数据</h2>")
            html_parts.append("<div class='meta'>")
            for key, value in meta.items():
                if isinstance(value, str) and len(value) < 200:
                    html_parts.append(f"<p><strong>{key}:</strong> {value}</p>")
            html_parts.append("</div>")
        
        # 主要内容
        content = data.get('content', {})
        if content:
            html_parts.append("<h2>内容</h2>")
            html_parts.append("<div class='content'>")
            
            extracted = content.get('extracted_content', {})
            if extracted:
                main_content = extracted.get('main_content', '')
                if main_content:
                    # 简单的段落分割
                    paragraphs = main_content.split('\n\n')
                    for para in paragraphs[:5]:  # 限制段落数量
                        if para.strip():
                            html_parts.append(f"<p>{para.strip()}</p>")
            
            html_parts.append("</div>")
        
        html_parts.extend(["</body>", "</html>"])
        
        return "\n".join(html_parts)
    
    def _convert_with_template(self, data: Dict[str, Any], template_name: str, **kwargs) -> str:
        """使用自定义模板转换"""
        try:
            template = self.jinja_env.get_template(template_name)
            return template.render(data=data, **kwargs)
        except Exception as e:
            self.logger.error(f"Template conversion failed: {e}")
            raise MLError(f"Template conversion failed: {e}")
    
    def _register_filters(self):
        """注册Jinja2自定义过滤器"""
        
        @self.jinja_env.filter('truncate_words')
        def truncate_words(text, count=50):
            """截断文本到指定单词数"""
            if not isinstance(text, str):
                return text
            words = text.split()
            if len(words) <= count:
                return text
            return ' '.join(words[:count]) + '...'
        
        @self.jinja_env.filter('clean_text')
        def clean_text(text):
            """清理文本"""
            if not isinstance(text, str):
                return text
            import re
            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
        
        @self.jinja_env.filter('format_date')
        def format_date(date_str, format='%Y-%m-%d'):
            """格式化日期"""
            if not date_str:
                return ''
            try:
                from dateutil.parser import parse
                dt = parse(date_str)
                return dt.strftime(format)
            except:
                return date_str
    
    def create_template(self, template_content: str, template_name: str):
        """创建自定义模板"""
        template = Template(template_content)
        # 在实际应用中，这里应该保存模板到文件系统
        return template
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        return ['json', 'xml', 'yaml', 'markdown', 'html']
