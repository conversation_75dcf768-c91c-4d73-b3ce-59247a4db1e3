"""
内容解析器模块

负责解析和预处理网页内容。
"""

import logging
import re
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin, urlparse
from datetime import datetime

from bs4 import BeautifulSoup, Comment
import html2text

from ..utils.exceptions import MLError


class ContentParser:
    """内容解析器
    
    功能特性:
    - HTML内容解析
    - 文本提取和清洗
    - 元数据提取
    - 结构化数据识别
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # HTML转Markdown转换器
        self.html2text = html2text.HTML2Text()
        self.html2text.ignore_links = False
        self.html2text.ignore_images = False
        self.html2text.body_width = 0  # 不限制行宽
    
    def parse(self, content: str, url: str) -> Dict[str, Any]:
        """解析网页内容
        
        Args:
            content: HTML内容
            url: 网页URL
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            # 创建BeautifulSoup对象
            soup = BeautifulSoup(content, 'html.parser')
            
            # 提取各种信息
            result = {
                'url': url,
                'title': self._extract_title(soup),
                'meta': self._extract_meta(soup),
                'text': self._extract_text(soup),
                'markdown': self._convert_to_markdown(content),
                'links': self._extract_links(soup, url),
                'images': self._extract_images(soup, url),
                'structure': self._analyze_structure(soup),
                'language': self._detect_language(soup),
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to parse content: {e}")
            raise MLError(f"Content parsing failed: {e}")
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取页面标题"""
        # 优先级顺序：title标签 -> h1标签 -> og:title -> 默认值
        
        # 1. title标签
        title_tag = soup.find('title')
        if title_tag and title_tag.get_text().strip():
            return title_tag.get_text().strip()
        
        # 2. h1标签
        h1_tag = soup.find('h1')
        if h1_tag and h1_tag.get_text().strip():
            return h1_tag.get_text().strip()
        
        # 3. og:title meta标签
        og_title = soup.find('meta', property='og:title')
        if og_title and og_title.get('content'):
            return og_title['content'].strip()
        
        # 4. 默认值
        return "Untitled"
    
    def _extract_meta(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """提取元数据"""
        meta = {}
        
        # 基本meta标签
        meta_tags = {
            'description': ['name', 'description'],
            'keywords': ['name', 'keywords'],
            'author': ['name', 'author'],
            'robots': ['name', 'robots'],
            'viewport': ['name', 'viewport'],
            'charset': ['charset', None]
        }
        
        for key, (attr, value) in meta_tags.items():
            if value:
                tag = soup.find('meta', {attr: value})
            else:
                tag = soup.find('meta', {attr: True})
            
            if tag:
                content = tag.get('content') or tag.get(attr)
                if content:
                    meta[key] = content.strip()
        
        # Open Graph标签
        og_tags = soup.find_all('meta', property=re.compile(r'^og:'))
        for tag in og_tags:
            property_name = tag.get('property', '').replace('og:', '')
            content = tag.get('content')
            if property_name and content:
                meta[f'og_{property_name}'] = content.strip()
        
        # Twitter Card标签
        twitter_tags = soup.find_all('meta', name=re.compile(r'^twitter:'))
        for tag in twitter_tags:
            name = tag.get('name', '').replace('twitter:', '')
            content = tag.get('content')
            if name and content:
                meta[f'twitter_{name}'] = content.strip()
        
        # JSON-LD结构化数据
        json_ld_scripts = soup.find_all('script', type='application/ld+json')
        if json_ld_scripts:
            import json
            json_ld_data = []
            for script in json_ld_scripts:
                try:
                    data = json.loads(script.string)
                    json_ld_data.append(data)
                except (json.JSONDecodeError, TypeError):
                    continue
            if json_ld_data:
                meta['json_ld'] = json_ld_data
        
        return meta
    
    def _extract_text(self, soup: BeautifulSoup) -> str:
        """提取纯文本内容"""
        # 移除不需要的标签
        for tag in soup(['script', 'style', 'nav', 'header', 'footer', 'aside']):
            tag.decompose()
        
        # 移除注释
        for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()
        
        # 提取文本
        text = soup.get_text(separator=' ', strip=True)
        
        # 清理文本
        text = re.sub(r'\s+', ' ', text)  # 合并多个空白字符
        text = re.sub(r'\n\s*\n', '\n\n', text)  # 规范化换行
        
        return text.strip()
    
    def _convert_to_markdown(self, content: str) -> str:
        """转换为Markdown格式"""
        try:
            return self.html2text.handle(content).strip()
        except Exception as e:
            self.logger.warning(f"Failed to convert to markdown: {e}")
            return ""
    
    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """提取链接"""
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href'].strip()
            text = link.get_text().strip()
            
            # 跳过空链接和JavaScript链接
            if not href or href.startswith(('javascript:', 'mailto:', 'tel:')):
                continue
            
            # 转换为绝对URL
            absolute_url = urljoin(base_url, href)
            
            # 提取链接属性
            link_data = {
                'url': absolute_url,
                'text': text,
                'title': link.get('title', ''),
                'rel': ' '.join(link.get('rel', [])),
                'target': link.get('target', '')
            }
            
            links.append(link_data)
        
        return links
    
    def _extract_images(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """提取图片"""
        images = []
        
        for img in soup.find_all('img'):
            src = img.get('src', '').strip()
            if not src:
                continue
            
            # 转换为绝对URL
            absolute_url = urljoin(base_url, src)
            
            # 提取图片属性
            img_data = {
                'url': absolute_url,
                'alt': img.get('alt', ''),
                'title': img.get('title', ''),
                'width': img.get('width', ''),
                'height': img.get('height', ''),
                'loading': img.get('loading', '')
            }
            
            images.append(img_data)
        
        return images
    
    def _analyze_structure(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """分析页面结构"""
        structure = {}
        
        # 标题层次结构
        headings = {}
        for level in range(1, 7):
            headings[f'h{level}'] = [
                h.get_text().strip() 
                for h in soup.find_all(f'h{level}')
            ]
        structure['headings'] = headings
        
        # 段落统计
        paragraphs = soup.find_all('p')
        structure['paragraph_count'] = len(paragraphs)
        structure['avg_paragraph_length'] = (
            sum(len(p.get_text()) for p in paragraphs) / len(paragraphs)
            if paragraphs else 0
        )
        
        # 列表统计
        structure['lists'] = {
            'unordered': len(soup.find_all('ul')),
            'ordered': len(soup.find_all('ol')),
            'definition': len(soup.find_all('dl'))
        }
        
        # 表格统计
        tables = soup.find_all('table')
        structure['tables'] = {
            'count': len(tables),
            'total_rows': sum(len(table.find_all('tr')) for table in tables),
            'total_cells': sum(len(table.find_all(['td', 'th'])) for table in tables)
        }
        
        # 表单统计
        forms = soup.find_all('form')
        structure['forms'] = {
            'count': len(forms),
            'inputs': len(soup.find_all('input')),
            'textareas': len(soup.find_all('textarea')),
            'selects': len(soup.find_all('select'))
        }
        
        # 媒体统计
        structure['media'] = {
            'images': len(soup.find_all('img')),
            'videos': len(soup.find_all('video')),
            'audios': len(soup.find_all('audio')),
            'iframes': len(soup.find_all('iframe'))
        }
        
        return structure
    
    def _detect_language(self, soup: BeautifulSoup) -> Optional[str]:
        """检测页面语言"""
        # 1. html标签的lang属性
        html_tag = soup.find('html')
        if html_tag and html_tag.get('lang'):
            return html_tag['lang']
        
        # 2. meta标签
        lang_meta = soup.find('meta', {'http-equiv': 'content-language'})
        if lang_meta and lang_meta.get('content'):
            return lang_meta['content']
        
        # 3. 其他lang属性
        lang_elements = soup.find_all(attrs={'lang': True})
        if lang_elements:
            return lang_elements[0]['lang']
        
        return None
    
    def extract_main_content(self, soup: BeautifulSoup) -> str:
        """提取主要内容"""
        # 尝试多种选择器来找到主要内容
        content_selectors = [
            'main',
            'article',
            '[role="main"]',
            '.main-content',
            '.content',
            '.post-content',
            '.entry-content',
            '.article-content',
            '#content',
            '#main'
        ]
        
        for selector in content_selectors:
            content = soup.select_one(selector)
            if content:
                # 移除不需要的子元素
                for unwanted in content.select('nav, aside, .sidebar, .ad, .advertisement'):
                    unwanted.decompose()
                
                return content.get_text(separator=' ', strip=True)
        
        # 如果没有找到特定的内容区域，返回body内容
        body = soup.find('body')
        if body:
            # 移除不需要的标签
            for tag in body(['nav', 'header', 'footer', 'aside', 'script', 'style']):
                tag.decompose()
            
            return body.get_text(separator=' ', strip=True)
        
        # 最后的备选方案
        return soup.get_text(separator=' ', strip=True)
    
    def extract_article_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """提取文章信息"""
        article_info = {}
        
        # 发布日期
        date_selectors = [
            'time[datetime]',
            '.published',
            '.date',
            '.post-date',
            '.article-date',
            '[itemprop="datePublished"]'
        ]
        
        for selector in date_selectors:
            date_elem = soup.select_one(selector)
            if date_elem:
                date_value = (
                    date_elem.get('datetime') or 
                    date_elem.get('content') or 
                    date_elem.get_text()
                )
                if date_value:
                    article_info['published_date'] = date_value.strip()
                    break
        
        # 作者信息
        author_selectors = [
            '[rel="author"]',
            '.author',
            '.byline',
            '.post-author',
            '.article-author',
            '[itemprop="author"]'
        ]
        
        for selector in author_selectors:
            author_elem = soup.select_one(selector)
            if author_elem:
                author_text = author_elem.get_text().strip()
                if author_text:
                    article_info['author'] = author_text
                    break
        
        # 分类/标签
        category_selectors = [
            '.category',
            '.categories',
            '.tags',
            '.post-category',
            '[rel="category"]'
        ]
        
        categories = []
        for selector in category_selectors:
            for elem in soup.select(selector):
                text = elem.get_text().strip()
                if text and text not in categories:
                    categories.append(text)
        
        if categories:
            article_info['categories'] = categories
        
        return article_info
