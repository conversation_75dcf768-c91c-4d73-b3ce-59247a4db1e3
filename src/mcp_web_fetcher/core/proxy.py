"""
代理管理模块

负责代理池的管理、轮换和健康检查。
"""

import asyncio
import logging
import random
from typing import List, Optional, Dict
from dataclasses import dataclass
from datetime import datetime, timedelta
from urllib.parse import urlparse

import aiohttp

from ..config import settings
from ..utils.exceptions import ProxyError


@dataclass
class ProxyInfo:
    """代理信息数据类"""
    
    url: str
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = "http"
    is_active: bool = True
    last_used: Optional[datetime] = None
    success_count: int = 0
    failure_count: int = 0
    response_time: float = 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 0.0
    
    @property
    def proxy_dict(self) -> Dict[str, str]:
        """转换为aiohttp代理格式"""
        return {
            "http": self.url,
            "https": self.url
        }


class ProxyManager:
    """代理管理器
    
    功能特性:
    - 代理池管理
    - 智能轮换策略
    - 健康检查
    - 性能监控
    - 故障转移
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.proxies: List[ProxyInfo] = []
        self.current_index = 0
        
        # 配置参数
        self.enabled = settings.proxy.enabled
        self.pool_size = settings.proxy.pool_size
        self.rotation_interval = settings.proxy.rotation_interval
        self.timeout = settings.proxy.timeout
        
        # 健康检查
        self._health_check_task: Optional[asyncio.Task] = None
        self._last_rotation = datetime.now()
        
        # 初始化代理池
        if self.enabled:
            self._init_proxy_pool()
    
    def _init_proxy_pool(self):
        """初始化代理池"""
        proxy_urls = settings.proxy.proxies
        
        if not proxy_urls:
            self.logger.warning("No proxies configured")
            self.enabled = False
            return
        
        for proxy_url in proxy_urls:
            try:
                proxy_info = self._parse_proxy_url(proxy_url)
                self.proxies.append(proxy_info)
                self.logger.info(f"Added proxy: {proxy_info.host}:{proxy_info.port}")
            except Exception as e:
                self.logger.error(f"Failed to parse proxy URL {proxy_url}: {e}")
        
        if not self.proxies:
            self.logger.warning("No valid proxies found")
            self.enabled = False
        else:
            self.logger.info(f"Initialized proxy pool with {len(self.proxies)} proxies")
            # 启动健康检查任务
            self._start_health_check()
    
    def _parse_proxy_url(self, proxy_url: str) -> ProxyInfo:
        """解析代理URL"""
        parsed = urlparse(proxy_url)
        
        if not parsed.hostname or not parsed.port:
            raise ValueError(f"Invalid proxy URL: {proxy_url}")
        
        return ProxyInfo(
            url=proxy_url,
            host=parsed.hostname,
            port=parsed.port,
            username=parsed.username,
            password=parsed.password,
            protocol=parsed.scheme or "http"
        )
    
    def _start_health_check(self):
        """启动健康检查任务"""
        if self._health_check_task is None or self._health_check_task.done():
            self._health_check_task = asyncio.create_task(self._health_check_loop())
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await self._check_all_proxies()
                await asyncio.sleep(60)  # 每分钟检查一次
            except Exception as e:
                self.logger.error(f"Health check error: {e}")
                await asyncio.sleep(60)
    
    async def _check_all_proxies(self):
        """检查所有代理的健康状态"""
        if not self.proxies:
            return
        
        self.logger.debug("Starting proxy health check")
        
        tasks = [self._check_proxy(proxy) for proxy in self.proxies]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        active_count = sum(1 for proxy in self.proxies if proxy.is_active)
        self.logger.info(f"Proxy health check completed. Active: {active_count}/{len(self.proxies)}")
    
    async def _check_proxy(self, proxy: ProxyInfo):
        """检查单个代理的健康状态"""
        test_url = "http://httpbin.org/ip"
        
        try:
            start_time = datetime.now()
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(test_url, proxy=proxy.url) as response:
                    if response.status == 200:
                        response_time = (datetime.now() - start_time).total_seconds()
                        proxy.response_time = response_time
                        proxy.success_count += 1
                        proxy.is_active = True
                        self.logger.debug(f"Proxy {proxy.host}:{proxy.port} is healthy")
                    else:
                        proxy.failure_count += 1
                        proxy.is_active = False
                        self.logger.warning(f"Proxy {proxy.host}:{proxy.port} returned status {response.status}")
                        
        except Exception as e:
            proxy.failure_count += 1
            proxy.is_active = False
            self.logger.warning(f"Proxy {proxy.host}:{proxy.port} health check failed: {e}")
    
    async def get_proxy(self) -> Optional[str]:
        """获取可用代理
        
        Returns:
            Optional[str]: 代理URL，如果没有可用代理则返回None
        """
        if not self.enabled or not self.proxies:
            return None
        
        # 检查是否需要轮换
        if self._should_rotate():
            self._rotate_proxy()
        
        # 获取活跃代理
        active_proxies = [p for p in self.proxies if p.is_active]
        
        if not active_proxies:
            self.logger.warning("No active proxies available")
            return None
        
        # 选择代理策略：优先选择成功率高、响应时间短的代理
        proxy = self._select_best_proxy(active_proxies)
        proxy.last_used = datetime.now()
        
        return proxy.url
    
    def _should_rotate(self) -> bool:
        """判断是否需要轮换代理"""
        time_since_rotation = datetime.now() - self._last_rotation
        return time_since_rotation.total_seconds() >= self.rotation_interval
    
    def _rotate_proxy(self):
        """轮换代理"""
        if self.proxies:
            self.current_index = (self.current_index + 1) % len(self.proxies)
            self._last_rotation = datetime.now()
            self.logger.debug(f"Rotated to proxy index: {self.current_index}")
    
    def _select_best_proxy(self, active_proxies: List[ProxyInfo]) -> ProxyInfo:
        """选择最佳代理
        
        综合考虑成功率和响应时间
        """
        if len(active_proxies) == 1:
            return active_proxies[0]
        
        # 计算综合得分 (成功率权重0.7，响应时间权重0.3)
        scored_proxies = []
        max_response_time = max(p.response_time for p in active_proxies) or 1.0
        
        for proxy in active_proxies:
            success_score = proxy.success_rate
            # 响应时间得分 (越小越好)
            time_score = 1.0 - (proxy.response_time / max_response_time)
            
            total_score = 0.7 * success_score + 0.3 * time_score
            scored_proxies.append((proxy, total_score))
        
        # 按得分排序，选择最高分的
        scored_proxies.sort(key=lambda x: x[1], reverse=True)
        
        # 在前3名中随机选择，增加随机性
        top_proxies = scored_proxies[:min(3, len(scored_proxies))]
        selected_proxy, _ = random.choice(top_proxies)
        
        return selected_proxy
    
    def record_success(self, proxy_url: str):
        """记录代理成功使用"""
        proxy = self._find_proxy_by_url(proxy_url)
        if proxy:
            proxy.success_count += 1
    
    def record_failure(self, proxy_url: str):
        """记录代理使用失败"""
        proxy = self._find_proxy_by_url(proxy_url)
        if proxy:
            proxy.failure_count += 1
            # 连续失败过多时标记为不活跃
            if proxy.failure_count > proxy.success_count * 2:
                proxy.is_active = False
    
    def _find_proxy_by_url(self, proxy_url: str) -> Optional[ProxyInfo]:
        """根据URL查找代理"""
        for proxy in self.proxies:
            if proxy.url == proxy_url:
                return proxy
        return None
    
    def get_stats(self) -> Dict:
        """获取代理池统计信息"""
        if not self.proxies:
            return {"enabled": False, "total": 0, "active": 0}
        
        active_count = sum(1 for p in self.proxies if p.is_active)
        avg_success_rate = sum(p.success_rate for p in self.proxies) / len(self.proxies)
        avg_response_time = sum(p.response_time for p in self.proxies if p.response_time > 0)
        avg_response_time = avg_response_time / len([p for p in self.proxies if p.response_time > 0]) if avg_response_time else 0
        
        return {
            "enabled": self.enabled,
            "total": len(self.proxies),
            "active": active_count,
            "avg_success_rate": avg_success_rate,
            "avg_response_time": avg_response_time,
            "proxies": [
                {
                    "host": p.host,
                    "port": p.port,
                    "is_active": p.is_active,
                    "success_rate": p.success_rate,
                    "response_time": p.response_time
                }
                for p in self.proxies
            ]
        }
    
    async def cleanup(self):
        """清理资源"""
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
