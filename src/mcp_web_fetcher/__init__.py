"""
MCP Web Fetcher - 基于机器学习的网页内容结构化提取服务

这是一个MCP (Model Context Protocol) 服务，专门用于将网页内容
转换为结构化数据，支持代理访问和批量处理。

主要功能:
- 智能网页内容抓取
- 基于ML/DL的内容结构化提取
- 代理池管理
- 批量并发处理
- 可视化监控

作者: MCP Web Fetcher Team
版本: 0.1.0
许可: MIT License
"""

__version__ = "0.1.0"
__author__ = "MCP Web Fetcher Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

# 导出主要组件
from .core.fetcher import WebFetcher
from .core.parser import ContentParser
from .core.converter import StructureConverter
from .ml.engine import MLEngine
from .server import MCPServer

__all__ = [
    "WebFetcher",
    "ContentParser", 
    "StructureConverter",
    "MLEngine",
    "MCPServer",
    "__version__",
    "__author__",
    "__email__",
    "__license__",
]
