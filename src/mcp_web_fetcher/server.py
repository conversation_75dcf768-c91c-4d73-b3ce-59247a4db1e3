"""
MCP服务器

实现Model Context Protocol服务器，提供网页抓取和结构化转换功能。
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Sequence
from datetime import datetime

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

from .config import settings
from .core.fetcher import WebFetcher
from .core.proxy import ProxyManager
from .core.cache import CacheManager
from .ml.engine import MLEngine
from .utils.logging import setup_logging


class MCPServer:
    """MCP Web Fetcher服务器
    
    提供以下工具:
    - fetch_url: 抓取单个URL
    - batch_fetch: 批量抓取URL
    - get_stats: 获取服务统计信息
    - clear_cache: 清理缓存
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.proxy_manager = ProxyManager()
        self.cache_manager = CacheManager()
        self.web_fetcher = WebFetcher(
            proxy_manager=self.proxy_manager,
            cache_manager=self.cache_manager
        )
        self.ml_engine = MLEngine()
        
        # MCP服务器
        self.server = Server("mcp-web-fetcher")
        
        # 注册工具
        self._register_tools()
        
        # 服务统计
        self.stats = {
            "requests_total": 0,
            "requests_success": 0,
            "requests_error": 0,
            "start_time": datetime.now()
        }
    
    def _register_tools(self):
        """注册MCP工具"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> ListToolsResult:
            """列出可用工具"""
            return ListToolsResult(
                tools=[
                    Tool(
                        name="fetch_url",
                        description="抓取单个URL并转换为结构化数据",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "url": {
                                    "type": "string",
                                    "description": "要抓取的URL"
                                },
                                "format": {
                                    "type": "string",
                                    "enum": ["json", "markdown", "text"],
                                    "default": "json",
                                    "description": "输出格式"
                                },
                                "use_proxy": {
                                    "type": "boolean",
                                    "default": False,
                                    "description": "是否使用代理"
                                },
                                "use_cache": {
                                    "type": "boolean", 
                                    "default": True,
                                    "description": "是否使用缓存"
                                },
                                "use_ml": {
                                    "type": "boolean",
                                    "default": True,
                                    "description": "是否使用ML处理"
                                }
                            },
                            "required": ["url"]
                        }
                    ),
                    Tool(
                        name="batch_fetch",
                        description="批量抓取多个URL并转换为结构化数据",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "urls": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "要抓取的URL列表"
                                },
                                "format": {
                                    "type": "string",
                                    "enum": ["json", "markdown", "text"],
                                    "default": "json",
                                    "description": "输出格式"
                                },
                                "use_proxy": {
                                    "type": "boolean",
                                    "default": False,
                                    "description": "是否使用代理"
                                },
                                "use_cache": {
                                    "type": "boolean",
                                    "default": True,
                                    "description": "是否使用缓存"
                                },
                                "use_ml": {
                                    "type": "boolean",
                                    "default": True,
                                    "description": "是否使用ML处理"
                                },
                                "max_concurrent": {
                                    "type": "integer",
                                    "default": 10,
                                    "minimum": 1,
                                    "maximum": 50,
                                    "description": "最大并发数"
                                }
                            },
                            "required": ["urls"]
                        }
                    ),
                    Tool(
                        name="get_stats",
                        description="获取服务统计信息",
                        inputSchema={
                            "type": "object",
                            "properties": {}
                        }
                    ),
                    Tool(
                        name="clear_cache",
                        description="清理缓存",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "pattern": {
                                    "type": "string",
                                    "description": "清理模式，如果不指定则清理所有缓存"
                                }
                            }
                        }
                    )
                ]
            )
        
        @self.server.call_tool()
        async def handle_call_tool(request: CallToolRequest) -> CallToolResult:
            """处理工具调用"""
            try:
                self.stats["requests_total"] += 1
                
                if request.name == "fetch_url":
                    result = await self._handle_fetch_url(request.arguments)
                elif request.name == "batch_fetch":
                    result = await self._handle_batch_fetch(request.arguments)
                elif request.name == "get_stats":
                    result = await self._handle_get_stats(request.arguments)
                elif request.name == "clear_cache":
                    result = await self._handle_clear_cache(request.arguments)
                else:
                    raise ValueError(f"Unknown tool: {request.name}")
                
                self.stats["requests_success"] += 1
                return result
                
            except Exception as e:
                self.stats["requests_error"] += 1
                self.logger.error(f"Tool call failed: {e}")
                
                return CallToolResult(
                    content=[
                        TextContent(
                            type="text",
                            text=f"Error: {str(e)}"
                        )
                    ],
                    isError=True
                )
    
    async def _handle_fetch_url(self, arguments: Dict[str, Any]) -> CallToolResult:
        """处理单URL抓取"""
        url = arguments["url"]
        format_type = arguments.get("format", "json")
        use_proxy = arguments.get("use_proxy", False)
        use_cache = arguments.get("use_cache", True)
        use_ml = arguments.get("use_ml", True)
        
        self.logger.info(f"Fetching URL: {url}")
        
        # 抓取网页
        fetch_result = await self.web_fetcher.fetch_url(
            url=url,
            use_proxy=use_proxy,
            use_cache=use_cache
        )
        
        if not fetch_result.success:
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"Failed to fetch URL: {fetch_result.error}"
                    )
                ],
                isError=True
            )
        
        # ML处理
        if use_ml:
            ml_result = await self.ml_engine.process(fetch_result)
            if ml_result.success:
                structured_data = ml_result.structured_data
            else:
                structured_data = {"raw_content": fetch_result.content}
        else:
            structured_data = {"raw_content": fetch_result.content}
        
        # 格式化输出
        formatted_content = self._format_output(structured_data, format_type)
        
        return CallToolResult(
            content=[
                TextContent(
                    type="text",
                    text=formatted_content
                )
            ]
        )
    
    async def _handle_batch_fetch(self, arguments: Dict[str, Any]) -> CallToolResult:
        """处理批量抓取"""
        urls = arguments["urls"]
        format_type = arguments.get("format", "json")
        use_proxy = arguments.get("use_proxy", False)
        use_cache = arguments.get("use_cache", True)
        use_ml = arguments.get("use_ml", True)
        max_concurrent = arguments.get("max_concurrent", 10)
        
        self.logger.info(f"Batch fetching {len(urls)} URLs")
        
        # 限制并发数
        original_max = self.web_fetcher.max_concurrent
        self.web_fetcher.max_concurrent = min(max_concurrent, original_max)
        
        try:
            # 批量抓取
            fetch_results = await self.web_fetcher.batch_fetch(
                urls=urls,
                use_proxy=use_proxy,
                use_cache=use_cache
            )
            
            # ML批量处理
            if use_ml:
                ml_results = await self.ml_engine.batch_process(fetch_results)
                structured_results = []
                
                for i, ml_result in enumerate(ml_results):
                    if ml_result.success:
                        structured_results.append(ml_result.structured_data)
                    else:
                        structured_results.append({
                            "url": fetch_results[i].url,
                            "error": ml_result.error,
                            "raw_content": fetch_results[i].content if fetch_results[i].success else ""
                        })
            else:
                structured_results = [
                    {
                        "url": result.url,
                        "raw_content": result.content if result.success else "",
                        "error": result.error if not result.success else None
                    }
                    for result in fetch_results
                ]
            
            # 格式化输出
            formatted_content = self._format_output(structured_results, format_type)
            
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=formatted_content
                    )
                ]
            )
            
        finally:
            # 恢复原始并发数
            self.web_fetcher.max_concurrent = original_max
    
    async def _handle_get_stats(self, arguments: Dict[str, Any]) -> CallToolResult:
        """处理统计信息获取"""
        stats = {
            "server": self.stats,
            "fetcher": await self.web_fetcher.cache_manager.get_stats(),
            "proxy": self.proxy_manager.get_stats(),
            "ml_engine": self.ml_engine.get_stats()
        }
        
        formatted_content = self._format_output(stats, "json")
        
        return CallToolResult(
            content=[
                TextContent(
                    type="text",
                    text=formatted_content
                )
            ]
        )
    
    async def _handle_clear_cache(self, arguments: Dict[str, Any]) -> CallToolResult:
        """处理缓存清理"""
        pattern = arguments.get("pattern")
        
        await self.cache_manager.clear(pattern)
        
        message = f"Cache cleared" + (f" with pattern: {pattern}" if pattern else " (all)")
        
        return CallToolResult(
            content=[
                TextContent(
                    type="text",
                    text=message
                )
            ]
        )
    
    def _format_output(self, data: Any, format_type: str) -> str:
        """格式化输出数据"""
        if format_type == "json":
            import json
            return json.dumps(data, ensure_ascii=False, indent=2)
        elif format_type == "markdown":
            return self._to_markdown(data)
        elif format_type == "text":
            return str(data)
        else:
            return str(data)
    
    def _to_markdown(self, data: Any) -> str:
        """转换为Markdown格式"""
        if isinstance(data, dict):
            lines = ["# 结构化数据\n"]
            for key, value in data.items():
                lines.append(f"## {key}\n")
                if isinstance(value, (dict, list)):
                    lines.append(f"```json\n{json.dumps(value, ensure_ascii=False, indent=2)}\n```\n")
                else:
                    lines.append(f"{value}\n")
            return "\n".join(lines)
        else:
            return f"```\n{data}\n```"
    
    async def initialize(self):
        """初始化服务器"""
        self.logger.info("Initializing MCP server...")
        
        # 初始化ML引擎
        await self.ml_engine.initialize()
        
        self.logger.info("MCP server initialized successfully")
    
    async def cleanup(self):
        """清理资源"""
        self.logger.info("Cleaning up MCP server...")
        
        await self.web_fetcher._cleanup()
        await self.proxy_manager.cleanup()
        await self.cache_manager.cleanup()
        await self.ml_engine.cleanup()
        
        self.logger.info("MCP server cleanup completed")


async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    # 创建服务器实例
    mcp_server = MCPServer()
    
    try:
        # 初始化服务器
        await mcp_server.initialize()
        
        # 运行服务器
        async with stdio_server() as (read_stream, write_stream):
            await mcp_server.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="mcp-web-fetcher",
                    server_version="0.1.0",
                    capabilities=mcp_server.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None
                    )
                )
            )
    
    except KeyboardInterrupt:
        logging.info("Received interrupt signal")
    
    except Exception as e:
        logging.error(f"Server error: {e}")
        raise
    
    finally:
        await mcp_server.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
