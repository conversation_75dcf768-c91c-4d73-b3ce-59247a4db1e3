"""
机器学习模型实现

包含各种用于内容处理的ML/DL模型。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import asyncio

import torch
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer
import spacy
from bs4 import BeautifulSoup

from ..config import settings


class BaseModel(ABC):
    """基础模型抽象类"""
    
    def __init__(self, device: torch.device):
        self.device = device
        self.logger = logging.getLogger(self.__class__.__name__)
        self.model = None
        self.is_loaded = False
    
    @abstractmethod
    async def load(self):
        """加载模型"""
        pass
    
    @abstractmethod
    async def predict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """模型预测"""
        pass
    
    def to_device(self, tensor):
        """将张量移动到指定设备"""
        return tensor.to(self.device)


class ContentExtractionModel(BaseModel):
    """内容提取模型
    
    使用BERT + BiLSTM + CRF架构进行命名实体识别和关键信息提取
    """
    
    def __init__(self, base_model, tokenizer, device: torch.device):
        super().__init__(device)
        self.base_model = base_model
        self.tokenizer = tokenizer
        
        # 标签映射
        self.label_map = {
            'O': 0, 'B-TITLE': 1, 'I-TITLE': 2,
            'B-CONTENT': 3, 'I-CONTENT': 4,
            'B-AUTHOR': 5, 'I-AUTHOR': 6,
            'B-DATE': 7, 'I-DATE': 8,
            'B-LINK': 9, 'I-LINK': 10
        }
        self.id_to_label = {v: k for k, v in self.label_map.items()}
    
    async def load(self):
        """加载内容提取模型"""
        try:
            # 创建模型架构
            self.model = ContentExtractionNet(
                base_model=self.base_model,
                num_labels=len(self.label_map),
                hidden_dim=256
            ).to(self.device)
            
            # 加载预训练权重 (如果存在)
            model_path = settings.ml.model_dir / "content_extraction.pth"
            if model_path.exists():
                self.model.load_state_dict(torch.load(model_path, map_location=self.device))
                self.logger.info("Loaded pre-trained content extraction model")
            else:
                self.logger.warning("No pre-trained model found, using base model")
            
            self.model.eval()
            self.is_loaded = True
            
        except Exception as e:
            self.logger.error(f"Failed to load content extraction model: {e}")
            raise
    
    async def extract(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取内容"""
        if not self.is_loaded:
            await self.load()
        
        try:
            text = data.get("text", "")
            soup = data.get("soup")
            
            # 使用规则和ML结合的方法
            extracted = {}
            
            # 1. 使用BeautifulSoup提取基本信息
            if soup:
                extracted.update(self._extract_with_rules(soup))
            
            # 2. 使用ML模型提取更复杂的信息
            if text and len(text.strip()) > 0:
                ml_extracted = await self._extract_with_ml(text)
                extracted.update(ml_extracted)
            
            return {
                "extracted_content": extracted,
                "confidence": 0.85  # 简化的置信度
            }
            
        except Exception as e:
            self.logger.error(f"Content extraction failed: {e}")
            return {"extracted_content": {}, "confidence": 0.0}
    
    def _extract_with_rules(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """基于规则的内容提取"""
        extracted = {}
        
        # 提取标题
        title = soup.find('title')
        if title:
            extracted['title'] = title.get_text().strip()
        
        # 提取主要内容
        content_selectors = [
            'article', 'main', '.content', '.post-content',
            '.entry-content', '.article-content'
        ]
        
        for selector in content_selectors:
            content = soup.select_one(selector)
            if content:
                extracted['main_content'] = content.get_text().strip()
                break
        
        # 提取作者信息
        author_selectors = [
            '.author', '.byline', '[rel="author"]',
            '.post-author', '.article-author'
        ]
        
        for selector in author_selectors:
            author = soup.select_one(selector)
            if author:
                extracted['author'] = author.get_text().strip()
                break
        
        # 提取日期
        date_selectors = [
            'time', '.date', '.published', '.post-date',
            '[datetime]', '.article-date'
        ]
        
        for selector in date_selectors:
            date_elem = soup.select_one(selector)
            if date_elem:
                date_text = date_elem.get('datetime') or date_elem.get_text()
                extracted['date'] = date_text.strip()
                break
        
        # 提取链接
        links = []
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text().strip()
            if text and href.startswith(('http', '/')):
                links.append({'url': href, 'text': text})
        
        extracted['links'] = links[:10]  # 限制链接数量
        
        return extracted
    
    async def _extract_with_ml(self, text: str) -> Dict[str, Any]:
        """基于ML的内容提取"""
        # 简化实现 - 在实际项目中会使用训练好的模型
        try:
            # 分词和编码
            inputs = self.tokenizer(
                text,
                max_length=self.base_model.config.max_position_embeddings,
                truncation=True,
                padding=True,
                return_tensors="pt"
            ).to(self.device)
            
            with torch.no_grad():
                # 获取基础模型输出
                outputs = self.base_model(**inputs)
                
                # 简化的实体识别 (实际应该使用训练好的分类头)
                # 这里只是示例实现
                return {
                    "ml_confidence": 0.8,
                    "entities": []  # 实际应该返回识别的实体
                }
                
        except Exception as e:
            self.logger.error(f"ML extraction failed: {e}")
            return {}


class LayoutAnalysisModel(BaseModel):
    """布局分析模型
    
    分析网页的视觉布局结构
    """
    
    def __init__(self, device: torch.device):
        super().__init__(device)
    
    async def load(self):
        """加载布局分析模型"""
        try:
            # 简化实现 - 实际应该加载LayoutLM或类似模型
            self.is_loaded = True
            self.logger.info("Layout analysis model loaded")
        except Exception as e:
            self.logger.error(f"Failed to load layout analysis model: {e}")
            raise
    
    async def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析布局"""
        if not self.is_loaded:
            await self.load()
        
        try:
            soup = data.get("soup")
            if not soup:
                return {"layout_info": {}, "confidence": 0.0}
            
            # 分析页面结构
            layout_info = self._analyze_structure(soup)
            
            return {
                "layout_info": layout_info,
                "confidence": 0.75
            }
            
        except Exception as e:
            self.logger.error(f"Layout analysis failed: {e}")
            return {"layout_info": {}, "confidence": 0.0}
    
    def _analyze_structure(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """分析页面结构"""
        structure = {}
        
        # 分析标题层次
        headings = {}
        for i in range(1, 7):
            headings[f'h{i}'] = len(soup.find_all(f'h{i}'))
        structure['headings'] = headings
        
        # 分析段落数量
        structure['paragraphs'] = len(soup.find_all('p'))
        
        # 分析列表
        structure['lists'] = {
            'ul': len(soup.find_all('ul')),
            'ol': len(soup.find_all('ol'))
        }
        
        # 分析表格
        structure['tables'] = len(soup.find_all('table'))
        
        # 分析图片
        structure['images'] = len(soup.find_all('img'))
        
        # 分析链接
        structure['links'] = len(soup.find_all('a', href=True))
        
        # 分析表单
        structure['forms'] = len(soup.find_all('form'))
        
        return structure


class ContentClassificationModel(BaseModel):
    """内容分类模型
    
    对网页内容进行主题分类
    """
    
    def __init__(self, base_model, tokenizer, device: torch.device):
        super().__init__(device)
        self.base_model = base_model
        self.tokenizer = tokenizer
        
        # 分类标签
        self.categories = [
            'news', 'blog', 'ecommerce', 'documentation',
            'social', 'entertainment', 'education', 'business',
            'technology', 'health', 'sports', 'other'
        ]
    
    async def load(self):
        """加载分类模型"""
        try:
            # 创建分类头
            self.classifier = nn.Linear(
                self.base_model.config.hidden_size,
                len(self.categories)
            ).to(self.device)
            
            # 加载预训练权重
            model_path = settings.ml.model_dir / "classification.pth"
            if model_path.exists():
                self.classifier.load_state_dict(torch.load(model_path, map_location=self.device))
                self.logger.info("Loaded pre-trained classification model")
            
            self.classifier.eval()
            self.is_loaded = True
            
        except Exception as e:
            self.logger.error(f"Failed to load classification model: {e}")
            raise
    
    async def classify(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分类内容"""
        if not self.is_loaded:
            await self.load()
        
        try:
            text = data.get("text", "")
            title = data.get("title", "")
            
            # 组合标题和内容
            combined_text = f"{title} {text}"[:512]  # 限制长度
            
            if not combined_text.strip():
                return {"classification": {}, "confidence": 0.0}
            
            # 简化的分类实现
            classification = self._classify_with_rules(combined_text, data.get("soup"))
            
            return {
                "classification": classification,
                "confidence": 0.7
            }
            
        except Exception as e:
            self.logger.error(f"Content classification failed: {e}")
            return {"classification": {}, "confidence": 0.0}
    
    def _classify_with_rules(self, text: str, soup: Optional[BeautifulSoup]) -> Dict[str, Any]:
        """基于规则的分类"""
        text_lower = text.lower()
        
        # 简单的关键词匹配
        if any(word in text_lower for word in ['news', 'breaking', 'report', 'journalist']):
            category = 'news'
        elif any(word in text_lower for word in ['blog', 'post', 'diary', 'personal']):
            category = 'blog'
        elif any(word in text_lower for word in ['buy', 'price', 'cart', 'shop', 'product']):
            category = 'ecommerce'
        elif any(word in text_lower for word in ['documentation', 'api', 'guide', 'tutorial']):
            category = 'documentation'
        elif any(word in text_lower for word in ['technology', 'tech', 'software', 'programming']):
            category = 'technology'
        else:
            category = 'other'
        
        return {
            "primary_category": category,
            "confidence_score": 0.7,
            "all_scores": {cat: 0.1 if cat != category else 0.7 for cat in self.categories}
        }


class ContentCleaningModel(BaseModel):
    """内容清洗模型
    
    识别和过滤噪声内容
    """
    
    def __init__(self, base_model, tokenizer, device: torch.device):
        super().__init__(device)
        self.base_model = base_model
        self.tokenizer = tokenizer
    
    async def load(self):
        """加载清洗模型"""
        try:
            self.is_loaded = True
            self.logger.info("Content cleaning model loaded")
        except Exception as e:
            self.logger.error(f"Failed to load cleaning model: {e}")
            raise
    
    async def clean(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗内容"""
        if not self.is_loaded:
            await self.load()
        
        try:
            soup = data.get("soup")
            if not soup:
                return {"cleaned_content": "", "confidence": 0.0}
            
            # 清洗HTML内容
            cleaned_soup = self._clean_html(soup)
            cleaned_text = cleaned_soup.get_text(separator=' ', strip=True)
            
            return {
                "cleaned_content": cleaned_text,
                "cleaned_html": str(cleaned_soup),
                "confidence": 0.8
            }
            
        except Exception as e:
            self.logger.error(f"Content cleaning failed: {e}")
            return {"cleaned_content": "", "confidence": 0.0}
    
    def _clean_html(self, soup: BeautifulSoup) -> BeautifulSoup:
        """清洗HTML内容"""
        # 创建副本避免修改原始内容
        cleaned_soup = BeautifulSoup(str(soup), 'html.parser')
        
        # 移除不需要的标签
        unwanted_tags = [
            'script', 'style', 'nav', 'header', 'footer',
            'aside', 'advertisement', 'ads', 'sidebar'
        ]
        
        for tag_name in unwanted_tags:
            for tag in cleaned_soup.find_all(tag_name):
                tag.decompose()
        
        # 移除具有特定class的元素
        unwanted_classes = [
            'ad', 'ads', 'advertisement', 'sidebar', 'nav',
            'navigation', 'menu', 'footer', 'header', 'banner'
        ]
        
        for class_name in unwanted_classes:
            for tag in cleaned_soup.find_all(class_=lambda x: x and class_name in ' '.join(x).lower()):
                tag.decompose()
        
        # 移除空的段落和div
        for tag in cleaned_soup.find_all(['p', 'div']):
            if not tag.get_text(strip=True):
                tag.decompose()
        
        return cleaned_soup


class ContentExtractionNet(nn.Module):
    """内容提取神经网络"""
    
    def __init__(self, base_model, num_labels: int, hidden_dim: int = 256):
        super().__init__()
        self.base_model = base_model
        self.dropout = nn.Dropout(0.1)
        self.lstm = nn.LSTM(
            base_model.config.hidden_size,
            hidden_dim // 2,
            bidirectional=True,
            batch_first=True
        )
        self.classifier = nn.Linear(hidden_dim, num_labels)
    
    def forward(self, input_ids, attention_mask=None, labels=None):
        outputs = self.base_model(input_ids=input_ids, attention_mask=attention_mask)
        sequence_output = outputs.last_hidden_state
        
        sequence_output = self.dropout(sequence_output)
        lstm_output, _ = self.lstm(sequence_output)
        logits = self.classifier(lstm_output)
        
        return logits
