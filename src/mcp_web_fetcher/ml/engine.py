"""
ML处理引擎

协调各个ML模型的处理流程，提供统一的接口。
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

import torch
from transformers import AutoTokenizer, AutoModel

from ..config import settings
from ..core.fetcher import FetchResult
from .models import (
    ContentExtractionModel,
    LayoutAnalysisModel,
    ContentClassificationModel,
    ContentCleaningModel
)


@dataclass
class ProcessingResult:
    """ML处理结果"""
    
    url: str
    structured_data: Dict[str, Any]
    metadata: Dict[str, Any]
    confidence_scores: Dict[str, float]
    processing_time: float
    timestamp: datetime
    success: bool
    error: Optional[str] = None


class MLEngine:
    """ML处理引擎
    
    功能特性:
    - 多模型协调处理
    - 可插拔模型架构
    - 动态模型加载
    - 性能监控
    - 批量处理优化
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.device = torch.device(settings.ml.device)
        self.batch_size = settings.ml.batch_size
        self.max_length = settings.ml.max_length
        
        # 模型实例
        self.content_extraction_model: Optional[ContentExtractionModel] = None
        self.layout_analysis_model: Optional[LayoutAnalysisModel] = None
        self.classification_model: Optional[ContentClassificationModel] = None
        self.cleaning_model: Optional[ContentCleaningModel] = None
        
        # 共享组件
        self.tokenizer = None
        self.base_model = None
        
        # 性能统计
        self.processing_stats = {
            "total_processed": 0,
            "total_time": 0.0,
            "success_count": 0,
            "error_count": 0
        }
        
        self.logger.info(f"MLEngine initialized with device: {self.device}")
    
    async def initialize(self):
        """初始化ML引擎"""
        try:
            await self._load_base_components()
            await self._load_models()
            self.logger.info("MLEngine initialization completed")
        except Exception as e:
            self.logger.error(f"Failed to initialize MLEngine: {e}")
            raise
    
    async def _load_base_components(self):
        """加载基础组件"""
        self.logger.info("Loading base components...")
        
        # 加载分词器
        self.tokenizer = AutoTokenizer.from_pretrained(
            settings.ml.content_extraction_model
        )
        
        # 加载基础模型
        self.base_model = AutoModel.from_pretrained(
            settings.ml.content_extraction_model
        ).to(self.device)
        
        self.logger.info("Base components loaded successfully")
    
    async def _load_models(self):
        """加载所有ML模型"""
        self.logger.info("Loading ML models...")
        
        # 内容提取模型
        self.content_extraction_model = ContentExtractionModel(
            base_model=self.base_model,
            tokenizer=self.tokenizer,
            device=self.device
        )
        await self.content_extraction_model.load()
        
        # 布局分析模型
        self.layout_analysis_model = LayoutAnalysisModel(
            device=self.device
        )
        await self.layout_analysis_model.load()
        
        # 内容分类模型
        self.classification_model = ContentClassificationModel(
            base_model=self.base_model,
            tokenizer=self.tokenizer,
            device=self.device
        )
        await self.classification_model.load()
        
        # 内容清洗模型
        self.cleaning_model = ContentCleaningModel(
            base_model=self.base_model,
            tokenizer=self.tokenizer,
            device=self.device
        )
        await self.cleaning_model.load()
        
        self.logger.info("All ML models loaded successfully")
    
    async def process(self, fetch_result: FetchResult) -> ProcessingResult:
        """处理单个抓取结果
        
        Args:
            fetch_result: 网页抓取结果
            
        Returns:
            ProcessingResult: ML处理结果
        """
        start_time = datetime.now()
        
        try:
            # 预处理
            preprocessed_data = await self._preprocess(fetch_result)
            
            # 并行执行各个模型
            tasks = [
                self._extract_content(preprocessed_data),
                self._analyze_layout(preprocessed_data),
                self._classify_content(preprocessed_data),
                self._clean_content(preprocessed_data)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 合并结果
            structured_data = await self._merge_results(results, fetch_result.url)
            
            # 计算置信度
            confidence_scores = self._calculate_confidence(results)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 更新统计
            self._update_stats(processing_time, True)
            
            return ProcessingResult(
                url=fetch_result.url,
                structured_data=structured_data,
                metadata={
                    "original_status": fetch_result.status_code,
                    "content_length": len(fetch_result.content),
                    "processing_time": processing_time,
                    "models_used": ["content_extraction", "layout_analysis", "classification", "cleaning"]
                },
                confidence_scores=confidence_scores,
                processing_time=processing_time,
                timestamp=datetime.now(),
                success=True
            )
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_stats(processing_time, False)
            
            self.logger.error(f"ML processing failed for {fetch_result.url}: {e}")
            
            return ProcessingResult(
                url=fetch_result.url,
                structured_data={},
                metadata={},
                confidence_scores={},
                processing_time=processing_time,
                timestamp=datetime.now(),
                success=False,
                error=str(e)
            )
    
    async def batch_process(self, fetch_results: List[FetchResult]) -> List[ProcessingResult]:
        """批量处理抓取结果
        
        Args:
            fetch_results: 抓取结果列表
            
        Returns:
            List[ProcessingResult]: 处理结果列表
        """
        # 分批处理以优化内存使用
        results = []
        
        for i in range(0, len(fetch_results), self.batch_size):
            batch = fetch_results[i:i + self.batch_size]
            
            # 并行处理批次
            batch_tasks = [self.process(result) for result in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # 处理异常结果
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    results.append(
                        ProcessingResult(
                            url=batch[j].url,
                            structured_data={},
                            metadata={},
                            confidence_scores={},
                            processing_time=0.0,
                            timestamp=datetime.now(),
                            success=False,
                            error=str(result)
                        )
                    )
                else:
                    results.append(result)
        
        return results
    
    async def _preprocess(self, fetch_result: FetchResult) -> Dict[str, Any]:
        """预处理数据"""
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(fetch_result.content, 'html.parser')
        
        # 提取基本信息
        title = soup.find('title')
        title_text = title.get_text().strip() if title else ""
        
        # 移除脚本和样式
        for script in soup(["script", "style"]):
            script.decompose()
        
        # 提取文本内容
        text_content = soup.get_text()
        
        # 清理文本
        lines = (line.strip() for line in text_content.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text_content = ' '.join(chunk for chunk in chunks if chunk)
        
        return {
            "url": fetch_result.url,
            "html": fetch_result.content,
            "soup": soup,
            "title": title_text,
            "text": text_content,
            "headers": fetch_result.headers,
            "metadata": fetch_result.metadata
        }
    
    async def _extract_content(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """内容提取"""
        if self.content_extraction_model:
            return await self.content_extraction_model.extract(data)
        return {}
    
    async def _analyze_layout(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """布局分析"""
        if self.layout_analysis_model:
            return await self.layout_analysis_model.analyze(data)
        return {}
    
    async def _classify_content(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """内容分类"""
        if self.classification_model:
            return await self.classification_model.classify(data)
        return {}
    
    async def _clean_content(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """内容清洗"""
        if self.cleaning_model:
            return await self.cleaning_model.clean(data)
        return {}
    
    async def _merge_results(self, results: List[Any], url: str) -> Dict[str, Any]:
        """合并各模型的结果"""
        merged = {
            "url": url,
            "content": {},
            "layout": {},
            "classification": {},
            "cleaned": {}
        }
        
        # 处理结果
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.warning(f"Model {i} failed: {result}")
                continue
            
            if i == 0:  # 内容提取
                merged["content"] = result
            elif i == 1:  # 布局分析
                merged["layout"] = result
            elif i == 2:  # 内容分类
                merged["classification"] = result
            elif i == 3:  # 内容清洗
                merged["cleaned"] = result
        
        return merged
    
    def _calculate_confidence(self, results: List[Any]) -> Dict[str, float]:
        """计算置信度分数"""
        confidence = {}
        
        model_names = ["content_extraction", "layout_analysis", "classification", "cleaning"]
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                confidence[model_names[i]] = 0.0
            else:
                # 从结果中提取置信度，如果没有则使用默认值
                confidence[model_names[i]] = result.get("confidence", 0.8)
        
        # 计算总体置信度
        valid_scores = [score for score in confidence.values() if score > 0]
        confidence["overall"] = sum(valid_scores) / len(valid_scores) if valid_scores else 0.0
        
        return confidence
    
    def _update_stats(self, processing_time: float, success: bool):
        """更新处理统计"""
        self.processing_stats["total_processed"] += 1
        self.processing_stats["total_time"] += processing_time
        
        if success:
            self.processing_stats["success_count"] += 1
        else:
            self.processing_stats["error_count"] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        total = self.processing_stats["total_processed"]
        
        return {
            "total_processed": total,
            "success_rate": self.processing_stats["success_count"] / total if total > 0 else 0.0,
            "average_processing_time": self.processing_stats["total_time"] / total if total > 0 else 0.0,
            "device": str(self.device),
            "models_loaded": {
                "content_extraction": self.content_extraction_model is not None,
                "layout_analysis": self.layout_analysis_model is not None,
                "classification": self.classification_model is not None,
                "cleaning": self.cleaning_model is not None
            }
        }
    
    async def cleanup(self):
        """清理资源"""
        if self.base_model:
            del self.base_model
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self.logger.info("MLEngine cleanup completed")
