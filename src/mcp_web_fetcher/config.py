"""
配置管理模块

使用Pydantic Settings进行配置管理，支持环境变量和配置文件。
"""

from typing import List, Optional
from pydantic import BaseSettings, Field, validator
from pathlib import Path


class DatabaseConfig(BaseSettings):
    """数据库配置"""
    
    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=5432, env="DB_PORT")
    name: str = Field(default="mcp_web_fetcher", env="DB_NAME")
    user: str = Field(default="postgres", env="DB_USER")
    password: str = Field(default="", env="DB_PASSWORD")
    
    @property
    def url(self) -> str:
        """构建数据库连接URL"""
        return f"postgresql+asyncpg://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


class RedisConfig(BaseSettings):
    """Redis配置"""
    
    host: str = Field(default="localhost", env="REDIS_HOST")
    port: int = Field(default=6379, env="REDIS_PORT")
    db: int = Field(default=0, env="REDIS_DB")
    password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    @property
    def url(self) -> str:
        """构建Redis连接URL"""
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.db}"


class MLConfig(BaseSettings):
    """机器学习配置"""
    
    model_dir: Path = Field(default=Path("models"), env="ML_MODEL_DIR")
    device: str = Field(default="cpu", env="ML_DEVICE")
    batch_size: int = Field(default=32, env="ML_BATCH_SIZE")
    max_length: int = Field(default=512, env="ML_MAX_LENGTH")
    
    # 模型配置
    content_extraction_model: str = Field(
        default="bert-base-multilingual-cased",
        env="ML_CONTENT_EXTRACTION_MODEL"
    )
    layout_analysis_model: str = Field(
        default="microsoft/layoutlm-base-uncased",
        env="ML_LAYOUT_ANALYSIS_MODEL"
    )
    classification_model: str = Field(
        default="roberta-base",
        env="ML_CLASSIFICATION_MODEL"
    )
    
    @validator("device")
    def validate_device(cls, v):
        """验证设备配置"""
        if v not in ["cpu", "cuda", "mps"]:
            raise ValueError("device must be one of: cpu, cuda, mps")
        return v


class ProxyConfig(BaseSettings):
    """代理配置"""
    
    enabled: bool = Field(default=False, env="PROXY_ENABLED")
    pool_size: int = Field(default=10, env="PROXY_POOL_SIZE")
    rotation_interval: int = Field(default=300, env="PROXY_ROTATION_INTERVAL")  # 秒
    timeout: int = Field(default=30, env="PROXY_TIMEOUT")
    
    # 代理列表 (格式: *********************:port)
    proxies: List[str] = Field(default_factory=list, env="PROXY_LIST")
    
    @validator("proxies", pre=True)
    def parse_proxies(cls, v):
        """解析代理列表"""
        if isinstance(v, str):
            return [p.strip() for p in v.split(",") if p.strip()]
        return v


class FetcherConfig(BaseSettings):
    """抓取器配置"""
    
    max_concurrent: int = Field(default=10, env="FETCHER_MAX_CONCURRENT")
    timeout: int = Field(default=30, env="FETCHER_TIMEOUT")
    retry_times: int = Field(default=3, env="FETCHER_RETRY_TIMES")
    retry_delay: float = Field(default=1.0, env="FETCHER_RETRY_DELAY")
    
    # 浏览器配置
    headless: bool = Field(default=True, env="FETCHER_HEADLESS")
    user_agent: str = Field(
        default="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        env="FETCHER_USER_AGENT"
    )
    
    # 缓存配置
    cache_enabled: bool = Field(default=True, env="FETCHER_CACHE_ENABLED")
    cache_ttl: int = Field(default=3600, env="FETCHER_CACHE_TTL")  # 秒


class MCPConfig(BaseSettings):
    """MCP服务配置"""
    
    host: str = Field(default="localhost", env="MCP_HOST")
    port: int = Field(default=8000, env="MCP_PORT")
    debug: bool = Field(default=False, env="MCP_DEBUG")
    
    # 认证配置
    auth_enabled: bool = Field(default=False, env="MCP_AUTH_ENABLED")
    secret_key: str = Field(default="your-secret-key", env="MCP_SECRET_KEY")
    
    # 限流配置
    rate_limit_enabled: bool = Field(default=True, env="MCP_RATE_LIMIT_ENABLED")
    rate_limit_requests: int = Field(default=100, env="MCP_RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=3600, env="MCP_RATE_LIMIT_WINDOW")  # 秒


class LoggingConfig(BaseSettings):
    """日志配置"""
    
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    file_enabled: bool = Field(default=True, env="LOG_FILE_ENABLED")
    file_path: Path = Field(default=Path("logs/app.log"), env="LOG_FILE_PATH")
    
    @validator("level")
    def validate_level(cls, v):
        """验证日志级别"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"log level must be one of: {valid_levels}")
        return v.upper()


class Settings(BaseSettings):
    """主配置类"""
    
    # 环境配置
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # 子配置
    database: DatabaseConfig = DatabaseConfig()
    redis: RedisConfig = RedisConfig()
    ml: MLConfig = MLConfig()
    proxy: ProxyConfig = ProxyConfig()
    fetcher: FetcherConfig = FetcherConfig()
    mcp: MCPConfig = MCPConfig()
    logging: LoggingConfig = LoggingConfig()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()
