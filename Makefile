# MCP Web Fetcher Makefile

.PHONY: help install dev-install test lint format clean build docker run stop logs

# 默认目标
help:
	@echo "MCP Web Fetcher - 可用命令:"
	@echo ""
	@echo "  install      - 安装项目依赖"
	@echo "  dev-install  - 安装开发依赖"
	@echo "  test         - 运行测试"
	@echo "  lint         - 代码检查"
	@echo "  format       - 代码格式化"
	@echo "  clean        - 清理临时文件"
	@echo "  build        - 构建项目"
	@echo "  docker       - 构建Docker镜像"
	@echo "  run          - 启动服务"
	@echo "  stop         - 停止服务"
	@echo "  logs         - 查看日志"

# 安装依赖
install:
	pip install -e .

# 安装开发依赖
dev-install:
	pip install -e ".[dev]"
	pre-commit install

# 运行测试
test:
	pytest tests/ -v --cov=mcp_web_fetcher --cov-report=html --cov-report=term

# 运行单元测试
test-unit:
	pytest tests/unit/ -v -m unit

# 运行集成测试
test-integration:
	pytest tests/integration/ -v -m integration

# 运行性能测试
test-performance:
	pytest tests/performance/ -v -m performance

# 代码检查
lint:
	flake8 src/ tests/
	mypy src/
	black --check src/ tests/
	isort --check-only src/ tests/

# 代码格式化
format:
	black src/ tests/
	isort src/ tests/

# 清理临时文件
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

# 构建项目
build: clean
	python -m build

# 构建Docker镜像
docker:
	docker build -t mcp-web-fetcher:latest .

# 构建开发Docker镜像
docker-dev:
	docker build --target development -t mcp-web-fetcher:dev .

# 启动服务 (Docker Compose)
run:
	docker-compose up -d

# 停止服务
stop:
	docker-compose down

# 查看日志
logs:
	docker-compose logs -f mcp-web-fetcher

# 重启服务
restart: stop run

# 进入容器
shell:
	docker-compose exec mcp-web-fetcher bash

# 数据库迁移
migrate:
	docker-compose exec mcp-web-fetcher alembic upgrade head

# 创建数据库迁移
migration:
	docker-compose exec mcp-web-fetcher alembic revision --autogenerate -m "$(MSG)"

# 安装Playwright浏览器
install-browsers:
	playwright install chromium

# 生成需求文件
requirements:
	pip-compile pyproject.toml

# 更新需求文件
requirements-update:
	pip-compile --upgrade pyproject.toml

# 安全检查
security:
	safety check
	bandit -r src/

# 文档生成
docs:
	sphinx-build -b html docs/ docs/_build/html

# 文档服务
docs-serve:
	sphinx-autobuild docs/ docs/_build/html

# 性能分析
profile:
	python -m cProfile -o profile.stats -m mcp_web_fetcher.server

# 监控
monitor:
	docker-compose exec prometheus promtool query instant 'up'

# 备份数据
backup:
	docker-compose exec postgres pg_dump -U postgres mcp_web_fetcher > backup_$(shell date +%Y%m%d_%H%M%S).sql

# 恢复数据
restore:
	docker-compose exec -T postgres psql -U postgres mcp_web_fetcher < $(FILE)

# 健康检查
health:
	curl -f http://localhost:8000/health || exit 1

# 压力测试
stress-test:
	locust -f tests/stress/locustfile.py --host=http://localhost:8000

# 代码覆盖率报告
coverage:
	coverage run -m pytest tests/
	coverage report
	coverage html

# 依赖检查
deps-check:
	pip-audit

# 版本发布
release:
	@echo "Current version: $(shell python -c 'import mcp_web_fetcher; print(mcp_web_fetcher.__version__)')"
	@read -p "Enter new version: " version; \
	sed -i "s/version = \".*\"/version = \"$$version\"/" pyproject.toml; \
	sed -i "s/__version__ = \".*\"/__version__ = \"$$version\"/" src/mcp_web_fetcher/__init__.py; \
	git add pyproject.toml src/mcp_web_fetcher/__init__.py; \
	git commit -m "Bump version to $$version"; \
	git tag "v$$version"; \
	echo "Version bumped to $$version. Don't forget to push: git push && git push --tags"

# 开发环境设置
dev-setup: dev-install install-browsers
	cp .env.example .env
	@echo "开发环境设置完成！"
	@echo "请编辑 .env 文件配置数据库和其他设置"

# 生产环境部署
deploy:
	docker-compose -f docker-compose.prod.yml up -d

# 全面检查
check: lint test security deps-check
	@echo "所有检查通过！"
