# 多阶段构建 Dockerfile
FROM python:3.11-slim as base

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 复制依赖文件
COPY pyproject.toml ./

# 安装Python依赖
RUN pip install --upgrade pip setuptools wheel && \
    pip install -e .

# 开发阶段
FROM base as development

# 安装开发依赖
RUN pip install -e ".[dev]"

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 8000

# 开发模式启动命令
CMD ["python", "-m", "mcp_web_fetcher.server"]

# 生产阶段
FROM base as production

# 创建非root用户
RUN groupadd -r mcp && useradd -r -g mcp mcp

# 复制源代码
COPY src/ ./src/
COPY README.md ./

# 安装Playwright浏览器
RUN playwright install chromium && \
    playwright install-deps chromium

# 创建必要目录
RUN mkdir -p /app/logs /app/models && \
    chown -R mcp:mcp /app

# 切换到非root用户
USER mcp

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000

# 生产模式启动命令
CMD ["python", "-m", "mcp_web_fetcher.server"]
