# 环境配置示例文件
# 复制此文件为 .env 并根据实际情况修改配置

# 环境设置
ENVIRONMENT=development

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mcp_web_fetcher
DB_USER=postgres
DB_PASSWORD=your_password_here

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 机器学习配置
ML_MODEL_DIR=./models
ML_DEVICE=cpu  # cpu, cuda, mps
ML_BATCH_SIZE=32
ML_MAX_LENGTH=512

# 模型配置
ML_CONTENT_EXTRACTION_MODEL=bert-base-multilingual-cased
ML_LAYOUT_ANALYSIS_MODEL=microsoft/layoutlm-base-uncased
ML_CLASSIFICATION_MODEL=roberta-base

# 代理配置
PROXY_ENABLED=false
PROXY_POOL_SIZE=10
PROXY_ROTATION_INTERVAL=300
PROXY_TIMEOUT=30
# 代理列表 (逗号分隔)
# PROXY_LIST=****************************,****************************

# 抓取器配置
FETCHER_MAX_CONCURRENT=10
FETCHER_TIMEOUT=30
FETCHER_RETRY_TIMES=3
FETCHER_RETRY_DELAY=1.0
FETCHER_HEADLESS=true
FETCHER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

# 缓存配置
FETCHER_CACHE_ENABLED=true
FETCHER_CACHE_TTL=3600

# MCP服务配置
MCP_HOST=localhost
MCP_PORT=8000
MCP_DEBUG=false

# 认证配置
MCP_AUTH_ENABLED=false
MCP_SECRET_KEY=your-secret-key-change-this-in-production

# 限流配置
MCP_RATE_LIMIT_ENABLED=true
MCP_RATE_LIMIT_REQUESTS=100
MCP_RATE_LIMIT_WINDOW=3600

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/app.log
